#!/usr/bin/env python3
"""
Test Token Handling Improvements
================================

Test the improved token handling, masking, and restoration functionality.
"""

import pandas as pd
import re
import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_improved_masking():
    """Test the improved masking functionality"""
    print("🎭 Testing improved masking...")
    
    # Test cases with complex mathematical expressions
    test_cases = [
        r'\lim_{n \to \infty} a_n = \frac{1}{2^\infty} = \frac{1}{\infty} = 0',
        r'a_n = \frac{1}{2^n}',
        r'\int_{0}^{\infty} e^{-x^2} dx = \frac{\sqrt{\pi}}{2}',
        r'\sum_{n=1}^{\infty} \frac{1}{n^2} = \frac{\pi^2}{6}',
        r'f(x) = x² + 2x + 1',
        r'∫x² dx from 0 to 5',
        r'√(x² + y²)',
        r'α + β = γ'
    ]
    
    # Simple masking function for testing
    def test_mask_expressions(text):
        if not isinstance(text, str):
            return text, {}
            
        mapping = {}
        masked_text = text
        token_counter = 1
        
        # Improved patterns (simplified for testing)
        patterns = [
            (r'\\lim_{[^}]*}\s*[^\\]*', 'LaTeX limit with subscript'),
            (r'\\frac\{[^{}]*\}\{[^{}]*\}', 'LaTeX fraction'),
            (r'\\int_{[^}]*}\^{[^}]*}[^\\]*?(?:dx|dy)', 'Definite integral'),
            (r'\\sum_{[^}]*}\^{[^}]*}[^\\]*', 'Summation with limits'),
            (r'[a-zA-Z0-9]+[²³⁴⁵⁶⁷⁸⁹⁰¹]+', 'Unicode superscript'),
            (r'∫[^∫\n]*?(?:dx|dy|dz)', 'Integral expression'),
            (r'√\([^)]+\)', 'Square root expression'),
            (r'[αβγδεζηθικλμνξοπρστυφχψω]', 'Greek letter'),
        ]
        
        for pattern, description in patterns:
            matches = []
            for match in re.finditer(pattern, masked_text):
                match_text = match.group()
                if not (match_text.startswith('[') and match_text.endswith(']')):
                    matches.append((match.start(), match.end(), match_text, description))
            
            for start, end, match_text, desc in reversed(matches):
                token = f'[MATH_{token_counter:03d}]'
                mapping[token] = {
                    'original': match_text,
                    'description': desc
                }
                masked_text = masked_text[:start] + token + masked_text[end:]
                token_counter += 1
                
        return masked_text, mapping
    
    total_expressions = 0
    successful_masks = 0
    
    for i, test_case in enumerate(test_cases):
        masked_text, mapping = test_mask_expressions(test_case)
        expressions_found = len(mapping)
        total_expressions += expressions_found
        
        if expressions_found > 0:
            successful_masks += 1
            print(f"   ✅ Case {i+1}: Found {expressions_found} expressions")
            print(f"      Original: {test_case}")
            print(f"      Masked:   {masked_text}")
        else:
            print(f"   ⚠️ Case {i+1}: No expressions found")
            print(f"      Text: {test_case}")
    
    print(f"✅ Masking test completed: {successful_masks}/{len(test_cases)} cases successful")
    print(f"   Total expressions detected: {total_expressions}")
    return total_expressions > 0

def test_token_protection():
    """Test token protection during translation simulation"""
    print("\n🛡️ Testing token protection...")
    
    # Simulate what might happen during translation
    test_text = "Find the limit [MATH_001] when [MATH_002] approaches infinity."
    
    # Simulate common translation modifications
    translation_modifications = [
        lambda x: x.replace('[MATH_', '[Math_'),  # Capitalization
        lambda x: x.replace('[MATH_', '[[MATH_').replace(']', ']]'),  # Double brackets
        lambda x: x.replace('[', '[ ').replace(']', ' ]'),  # Added spaces
        lambda x: x.replace('_', ' _'),  # Space before underscore
    ]
    
    # Test protection mechanism
    def protect_tokens(text):
        tokens = re.findall(r'\[MATH_\d+\]', text)
        protected_text = text
        token_map = {}
        
        for i, token in enumerate(tokens):
            placeholder = f"MATHTOKEN{i:04d}PLACEHOLDER"
            token_map[placeholder] = token
            protected_text = protected_text.replace(token, placeholder)
        
        return protected_text, token_map
    
    def restore_tokens(text, token_map):
        restored_text = text
        for placeholder, original_token in token_map.items():
            restored_text = restored_text.replace(placeholder, original_token)
        return restored_text
    
    # Test each modification
    protection_successful = 0
    for i, modify_func in enumerate(translation_modifications):
        # Protect tokens
        protected_text, token_map = protect_tokens(test_text)
        
        # Simulate translation modification on protected text
        modified_text = modify_func(protected_text)
        
        # Restore tokens
        restored_text = restore_tokens(modified_text, token_map)
        
        # Check if original tokens are preserved
        original_tokens = re.findall(r'\[MATH_\d+\]', test_text)
        restored_tokens = re.findall(r'\[MATH_\d+\]', restored_text)
        
        if original_tokens == restored_tokens:
            protection_successful += 1
            print(f"   ✅ Protection test {i+1}: Tokens preserved")
        else:
            print(f"   ❌ Protection test {i+1}: Tokens modified")
            print(f"      Original: {original_tokens}")
            print(f"      Restored: {restored_tokens}")
    
    print(f"✅ Token protection: {protection_successful}/{len(translation_modifications)} tests passed")
    return protection_successful == len(translation_modifications)

def test_enhanced_restoration():
    """Test enhanced restoration with token variations"""
    print("\n🔁 Testing enhanced restoration...")
    
    # Original mapping
    original_mapping = {
        '[MATH_001]': {'original': r'\frac{a}{b}', 'description': 'LaTeX fraction'},
        '[MATH_002]': {'original': 'x²', 'description': 'Unicode superscript'},
        '[MATH_003]': {'original': '∫x dx', 'description': 'Integral expression'}
    }
    
    # Test texts with various token corruptions
    test_cases = [
        "Calculate [MATH_001] when a = 4",  # Normal case
        "Calculate [Math_001] when a = 4",  # Capitalization change
        "Calculate [[MATH_001]] when a = 4",  # Double brackets
        "Calculate [[Math_001]] when a = 4",  # Double brackets + capitalization
        "Calculate [ MATH_001 ] when a = 4",  # Added spaces
        "Calculate [MATH _001] when a = 4",  # Space before underscore
    ]
    
    # Enhanced restoration function
    def enhanced_restore(text, mapping):
        if not isinstance(text, str):
            return text
            
        restored_text = text
        extended_mapping = {}
        
        for original_token, token_info in mapping.items():
            original_expression = token_info['original']
            
            # Add variations
            variations = [
                original_token,
                original_token.replace('[MATH_', '[Math_'),
                original_token.replace('[MATH_', '[[MATH_').replace(']', ']]'),
                original_token.replace('[MATH_', '[[Math_').replace(']', ']]'),
                original_token.replace('[', '[ ').replace(']', ' ]'),
                original_token.replace('_', ' _'),
            ]
            
            for variation in variations:
                extended_mapping[variation] = original_expression
        
        # Sort by length (longest first)
        sorted_tokens = sorted(extended_mapping.keys(), key=len, reverse=True)
        
        for token in sorted_tokens:
            if token in restored_text:
                restored_text = restored_text.replace(token, extended_mapping[token])
        
        return restored_text
    
    successful_restorations = 0
    for i, test_case in enumerate(test_cases):
        restored = enhanced_restore(test_case, original_mapping)
        
        # Check if mathematical expressions are restored
        has_math_expressions = any(info['original'] in restored for info in original_mapping.values())
        
        if has_math_expressions:
            successful_restorations += 1
            print(f"   ✅ Restoration {i+1}: Success")
            print(f"      Input:  {test_case}")
            print(f"      Output: {restored}")
        else:
            print(f"   ❌ Restoration {i+1}: Failed")
            print(f"      Input:  {test_case}")
            print(f"      Output: {restored}")
    
    print(f"✅ Enhanced restoration: {successful_restorations}/{len(test_cases)} tests passed")
    return successful_restorations >= len(test_cases) * 0.8  # 80% success rate

def main():
    """Run all token handling tests"""
    print("🧪 Token Handling Improvement Tests")
    print("=" * 50)
    
    all_tests_passed = True
    
    # Test 1: Improved masking
    if not test_improved_masking():
        all_tests_passed = False
    
    # Test 2: Token protection
    if not test_token_protection():
        all_tests_passed = False
    
    # Test 3: Enhanced restoration
    if not test_enhanced_restoration():
        all_tests_passed = False
    
    print("\n" + "=" * 50)
    
    if all_tests_passed:
        print("🎉 ALL TOKEN HANDLING TESTS PASSED!")
        print("✅ The token handling improvements are working correctly.")
        print("✅ Mathematical expressions should now be properly preserved.")
    else:
        print("⚠️ SOME TOKEN HANDLING TESTS FAILED!")
        print("❌ There may still be issues with token preservation.")
    
    return all_tests_passed

if __name__ == "__main__":
    main()
