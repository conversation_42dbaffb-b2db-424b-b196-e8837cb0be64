# Mathematical Expression Masking Tool

A user-friendly GUI application for masking and restoring mathematical expressions in datasets.

## Features

### 🎭 Masking
- Convert complex mathematical expressions into short tokens
- Handle LaTeX expressions (`\frac{a}{b}`, `\sqrt{x}`, etc.)
- Process Unicode mathematical symbols (∫, √, ², ³, etc.)
- Support for subscripts, superscripts, and mathematical notation
- Process Question, Answer Key, and Solution columns
- Generate detailed mapping tables for restoration

### 🔄 Restoration
- Convert tokens back to original mathematical expressions
- Use mapping tables to restore exact original content
- Preserve all mathematical formatting
- Create comparison files with both masked and restored content

### 💻 User Interface
- Clean, tabbed interface
- Progress tracking for long operations
- Detailed results and logging
- File browser integration
- Error handling and validation

## Installation

1. **Prerequisites**: Python 3.6+ with pandas
   ```bash
   pip install pandas
   ```

2. **Download**: Save all files to a folder:
   - `math_masking_ui.py` - Main application
   - `launch_ui.py` - Launcher script
   - `README.md` - This file

## Usage

### Starting the Application
```bash
python launch_ui.py
```

### Tab 1: Mask Mathematical Expressions

1. **Select Input File**: Choose your Excel (.xlsx) or CSV (.csv) file
2. **Set Output Prefix**: Enter a name for your output files (e.g., "my_dataset")
3. **Click "Process & Mask Expressions"**

**Output Files:**
- `{prefix}_dataset.csv` - Masked dataset with tokens
- `{prefix}_mapping.csv` - Token-to-expression mapping table

### Tab 2: Restore Original Expressions

1. **Select Mapping File**: Choose the mapping CSV file from masking step
2. **Select Masked Dataset**: Choose the masked dataset CSV file
3. **Click "Restore Original Expressions"**

**Output File:**
- `{dataset_name}_RESTORED.csv` - Dataset with both masked and restored columns

### Tab 3: About
- Information about the tool
- Supported file formats
- Usage instructions

## File Formats

### Input Files
- **Excel**: `.xlsx` files with columns: Question, Answer_key, Solution
- **CSV**: `.csv` files with the same column structure

### Output Files
- **CSV format** (recommended for mathematical content)
- Preserves all Unicode and special characters
- Compatible with Excel and other tools

## Example Workflow

1. **Start with your dataset**: `my_data.xlsx`
2. **Mask expressions**: 
   - Input: `my_data.xlsx`
   - Output: `my_data_dataset.csv`, `my_data_mapping.csv`
3. **Process your data** using the masked dataset
4. **Restore expressions**:
   - Input: `my_data_mapping.csv`, `my_data_dataset.csv`
   - Output: `my_data_dataset_RESTORED.csv`

## Supported Mathematical Expressions

- **LaTeX**: `\frac{a}{b}`, `\sqrt{x}`, `\int`, `\sum`, etc.
- **Unicode symbols**: ∫, √, ∞, ±, ℝ, θ, etc.
- **Superscripts**: x², y³, a⁴, etc.
- **Subscripts**: x₁, y₂, a₃, etc.
- **Fractions**: ½, ⅓, ¼, ¾, etc.
- **Complex expressions**: Complete integral expressions, nested fractions

## Troubleshooting

### Common Issues

1. **"File not found" error**
   - Make sure the file path is correct
   - Check file permissions

2. **"No columns found" error**
   - Ensure your file has columns named: Question, Answer_key, Solution
   - Check that the file is not corrupted

3. **Unicode display issues**
   - Use CSV format for best compatibility
   - The tool automatically converts problematic characters

4. **Large file processing**
   - The tool shows progress bars for long operations
   - Be patient with large datasets (1000+ rows)

### Getting Help

If you encounter issues:
1. Check the error messages in the application
2. Verify your input file format
3. Make sure all required columns exist
4. Try with a smaller test file first

## Technical Details

- **Language**: Python 3.6+
- **GUI Framework**: tkinter (built-in)
- **Data Processing**: pandas
- **File Formats**: CSV (primary), Excel (input)
- **Encoding**: UTF-8 with ASCII fallbacks

## License

This tool is provided as-is for educational and research purposes.
