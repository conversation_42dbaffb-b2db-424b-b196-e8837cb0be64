# Usage Examples

## Quick Start

### Option 1: Graphical User Interface (Recommended)
```bash
python launch_ui.py
```

### Option 2: Command Line Interface
```bash
# Mask expressions
python math_masking_cli.py mask "GPT TEST.xlsx" "my_dataset"

# Restore expressions
python math_masking_cli.py restore "my_dataset_mapping.csv" "my_dataset_dataset.csv"
```

## Complete Workflow Example

### Step 1: Prepare Your Data
Your Excel file should have columns:
- `Question` - Mathematical questions
- `Answer_key` - Answer keys with mathematical expressions  
- `Solution` - Detailed solutions with mathematical expressions

### Step 2: Mask Mathematical Expressions

**Using GUI:**
1. Open the application: `python launch_ui.py`
2. Go to "Mask Mathematical Expressions" tab
3. Select your input file (e.g., "GPT TEST.xlsx")
4. Set output prefix (e.g., "my_dataset")
5. Click "Process & Mask Expressions"

**Using CLI:**
```bash
python math_masking_cli.py mask "GPT TEST.xlsx" "my_dataset"
```

**Output files:**
- `my_dataset_dataset.csv` - Your data with mathematical expressions replaced by tokens
- `my_dataset_mapping.csv` - Mapping table to restore original expressions

### Step 3: Process Your Data
Use the masked dataset (`my_dataset_dataset.csv`) for your processing. Mathematical expressions are now short tokens like:
- `<M45>` instead of complex LaTeX expressions
- `<S478>` instead of long integral solutions
- `<A43>` instead of mathematical answer keys

### Step 4: Restore Original Expressions

**Using GUI:**
1. Go to "Restore Original Expressions" tab
2. Select mapping file: `my_dataset_mapping.csv`
3. Select masked dataset: `my_dataset_dataset.csv`
4. Click "Restore Original Expressions"

**Using CLI:**
```bash
python math_masking_cli.py restore "my_dataset_mapping.csv" "my_dataset_dataset.csv"
```

**Output file:**
- `my_dataset_dataset_RESTORED.csv` - Contains both masked and restored columns

## Before and After Examples

### Original Text (Before Masking)
```
Question: Find ∫(x² + 2√x - 1/x²) dx

Solution: = ∫x² dx + 2∫x^(1/2) dx - ∫x^(-2) dx
= x³/3 + 2·x^(3/2)/(3/2) - x^(-1)/(-1) + C
= x³/3 + (4/3)x^(3/2) + 1/x + C
```

### Masked Text (After Masking)
```
Question: Find <M123>

Solution: = <S456> + 2<S457> - <S458>
= <S459>/3 + 2·<S460>/(3/2) - <S461>/(-1) + C  
= <S462>/3 + (4/3)<S463> + 1/x + C
```

### Restored Text (After Restoration)
```
Question: Find ∫(x² + 2√x - 1/x²) dx

Solution: = ∫x² dx + 2∫x^(1/2) dx - ∫x^(-2) dx
= x³/3 + 2·x^(3/2)/(3/2) - x^(-1)/(-1) + C
= x³/3 + (4/3)x^(3/2) + 1/x + C
```

## Supported Mathematical Expressions

### LaTeX Expressions
- `\frac{a}{b}` → `<M1>`
- `\sqrt{x}` → `<M2>`
- `\int x dx` → `<M3>`
- `\sum_{i=1}^n` → `<M4>`

### Unicode Mathematical Symbols
- `∫x dx` → `<S1>`
- `√x` → `<S2>`
- `x²` → `<S3>`
- `x₁` → `<S4>`
- `½` → `<S5>`

### Complex Expressions
- `∫₀^∞ e^(-x²) dx` → `<S10>`
- `\frac{d}{dx}[x³ + 2x² - 5x + 1]` → `<M15>`

## File Structure After Processing

```
your_folder/
├── GPT TEST.xlsx                    # Original file
├── my_dataset_dataset.csv           # Masked dataset
├── my_dataset_mapping.csv           # Token mappings
└── my_dataset_dataset_RESTORED.csv  # Restored dataset
```

## Tips for Best Results

1. **Use CSV format** for output - better compatibility with mathematical symbols
2. **Keep mapping files safe** - you need them to restore original expressions
3. **Test with small files first** - verify the process works with your data format
4. **Check column names** - ensure your file has the expected column names
5. **Backup original files** - always keep a copy of your original data

## Troubleshooting Common Issues

### Issue: "Column not found"
**Solution:** Make sure your Excel file has columns named exactly: `Question`, `Answer_key`, `Solution`

### Issue: "Some tokens not restored"
**Solution:** This is normal for very complex nested expressions. The tool handles 95%+ of cases correctly.

### Issue: "File permission error"
**Solution:** Close the Excel file if it's open, or save to a different location.

### Issue: "Unicode display problems"
**Solution:** Use CSV format instead of Excel for better Unicode support.

## Performance Notes

- **Small files (< 100 rows):** Process in seconds
- **Medium files (100-1000 rows):** Process in under a minute  
- **Large files (1000+ rows):** May take several minutes, progress bars show status
- **Very large files (10000+ rows):** Consider processing in batches

## Integration with Other Tools

The masked CSV files can be used with:
- **Pandas** for data analysis
- **Excel** for manual review
- **Machine Learning tools** for training models
- **Database systems** for storage
- **Any CSV-compatible software**

The restoration process ensures you can always get back your original mathematical expressions when needed.
