# Language Support Guide
## Mathematical Expression Translation Pipeline

### 🌍 Comprehensive Language Support

The Mathematical Expression Translation Pipeline now supports **21+ languages** across multiple translation services, ensuring you can translate educational content to virtually any major language while preserving mathematical expressions.

## 📋 Complete Language List

### 🌐 **Major International Languages**
| Language | Code | DeepL | Google | BhashaIndia |
|----------|------|-------|--------|-------------|
| English | en | ✅ | ✅ | ✅ |
| Spanish | es | ✅ | ✅ | ✅ |
| French | fr | ✅ | ✅ | ✅ |
| German | de | ✅ | ✅ | ✅ |
| Portuguese | pt | ✅ | ✅ | ✅ |
| Russian | ru | ✅ | ✅ | ✅ |
| Chinese | zh | ✅ | ✅ | ✅ |
| Japanese | ja | ✅ | ✅ | ✅ |
| Korean | ko | ✅ | ✅ | ✅ |
| Arabic | ar | ✅ | ✅ | ✅ |

### 🇮🇳 **Indian Subcontinent Languages**
| Language | Code | DeepL | Google | BhashaIndia |
|----------|------|-------|--------|-------------|
| Hindi | hi | ✅ | ✅ | ✅ |
| Urdu | ur | ✅ | ✅ | ✅ |
| Bengali | bn | ❌ | ✅ | ✅ |
| Tamil | ta | ❌ | ✅ | ✅ |
| Telugu | te | ❌ | ✅ | ✅ |
| Malayalam | ml | ❌ | ✅ | ✅ |
| Kannada | kn | ❌ | ✅ | ✅ |
| Gujarati | gu | ❌ | ✅ | ✅ |
| Punjabi | pa | ❌ | ✅ | ✅ |
| Odia | or | ❌ | ✅ | ✅ |
| Assamese | as | ❌ | ✅ | ✅ |
| Marathi | mr | ❌ | ✅ | ✅ |

### 🌏 **Southeast Asian Languages**
| Language | Code | DeepL | Google | BhashaIndia |
|----------|------|-------|--------|-------------|
| Bahasa Indonesia | id | ✅ | ✅ | ✅ |

## 🎯 **Service Recommendations**

### 🥇 **For European Languages**
**Use DeepL** - Best quality for:
- English ↔ Spanish, French, German, Portuguese, Russian
- Professional-grade accuracy
- Excellent context understanding

### 🥇 **For Indian Languages**
**Use BhashaIndia** - Specialized for:
- Hindi, Urdu, Bengali, Tamil, Telugu, Malayalam
- Kannada, Gujarati, Punjabi, Odia, Assamese, Marathi
- Cultural context awareness
- Regional language expertise

### 🥇 **For Southeast Asian Languages**
**Use BhashaIndia or DeepL** for:
- Bahasa Indonesia
- Both services provide good quality

### 🥉 **For Quick Testing**
**Use Google Translate** - Free option:
- All languages supported
- No API key required
- Lower quality but instant results

## 🔧 **Setup Instructions by Service**

### DeepL Setup
1. Visit: https://www.deepl.com/pro-api
2. Create free account (500K characters/month)
3. Copy API key
4. Paste in application settings

### BhashaIndia Setup
1. Visit: https://bhashaindia.com/api
2. Register for account
3. Get API key
4. Paste in application settings

### Google Translate Setup
1. Select "Google Translate" option
2. No API key needed
3. Install library: `pip install googletrans==4.0.0-rc1`

## 📊 **Translation Quality Comparison**

| Service | Quality | Speed | Cost | Best For |
|---------|---------|-------|------|----------|
| **DeepL** | 🌟🌟🌟🌟🌟 | Fast | Free tier | European languages |
| **BhashaIndia** | 🌟🌟🌟🌟🌟 | Fast | Paid | Indian languages |
| **Google Translate** | 🌟🌟🌟 | Very Fast | Free | Quick testing |

## 🎯 **Use Case Examples**

### **Educational Content Translation**
```
English → Hindi (Math textbook)
Service: BhashaIndia
Quality: Excellent for educational terminology

English → Spanish (University course)
Service: DeepL  
Quality: Professional academic translation

English → Bahasa Indonesia (Online course)
Service: BhashaIndia or DeepL
Quality: High accuracy for technical content
```

### **Mathematical Expression Preservation**
All services preserve mathematical expressions perfectly:
- LaTeX: `\frac{a}{b}` → stays as `\frac{a}{b}`
- Unicode: `x²` → stays as `x²`
- Complex: `∫₀^∞ e^(-x²) dx` → stays intact

## 🚀 **Getting Started**

1. **Choose your target language** from the 21+ supported options
2. **Select appropriate service** based on recommendations above
3. **Get API key** (if required)
4. **Start translating** with perfect math preservation!

## 💡 **Pro Tips**

- **Test first**: Try with 5-10 rows before processing large datasets
- **Mix services**: Use different services for different language pairs
- **Quality check**: Always review the comparison report
- **Backup**: Keep original files safe (automatic backups included)

---

**🎉 Result**: Professional-quality translation of educational content in 21+ languages with perfect mathematical expression preservation!
