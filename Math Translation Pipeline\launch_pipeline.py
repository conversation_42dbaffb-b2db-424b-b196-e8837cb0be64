#!/usr/bin/env python3
"""
Mathematical Expression Translation Pipeline - Launcher
======================================================

Launch the Mathematical Expression Translation Pipeline application.

This tool provides a complete workflow for translating educational content
while preserving mathematical expressions:

🎭 Phase 1: Masking - Replace LaTeX/math with tokens
🌐 Phase 2: Translation - Translate text while preserving tokens  
🔁 Phase 3: Restoration - Replace tokens with original expressions

Usage:
    python launch_pipeline.py

Requirements:
    - Python 3.6+
    - pandas
    - requests
    - tkinter (usually included with Python)
    - googletrans (optional, for Google Translate)

Author: AI Assistant
Version: 1.0
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_dependencies():
    """Check if all required dependencies are installed"""
    missing_deps = []
    optional_deps = []

    # Required dependencies
    try:
        import pandas
    except ImportError:
        missing_deps.append("pandas")

    try:
        import requests
    except ImportError:
        missing_deps.append("requests")

    try:
        import tkinter
    except ImportError:
        missing_deps.append("tkinter")

    try:
        import openpyxl
    except ImportError:
        missing_deps.append("openpyxl")

    # Optional dependencies
    try:
        import googletrans
    except ImportError:
        optional_deps.append("googletrans==4.0.0-rc1")

    return missing_deps, optional_deps

def install_dependencies(deps):
    """Install missing dependencies"""
    import subprocess
    
    print("Installing missing dependencies...")
    for dep in deps:
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
            print(f"✅ {dep} installed successfully")
        except subprocess.CalledProcessError:
            print(f"❌ Failed to install {dep}")
            return False
    return True

def main():
    print("🚀 Starting Mathematical Expression Translation Pipeline...")
    print("=" * 60)

    # Check dependencies
    missing_deps, optional_deps = check_dependencies()

    if missing_deps:
        print(f"❌ Missing required dependencies: {', '.join(missing_deps)}")

        response = input("Would you like to install them automatically? (y/n): ")
        if response.lower() in ['y', 'yes']:
            if install_dependencies(missing_deps):
                print("✅ All required dependencies installed successfully!")
            else:
                print("❌ Failed to install some dependencies. Please install manually:")
                for dep in missing_deps:
                    print(f"   pip install {dep}")
                sys.exit(1)
        else:
            print("Please install the missing dependencies manually:")
            for dep in missing_deps:
                print(f"   pip install {dep}")
            sys.exit(1)

    if optional_deps:
        print(f"ℹ️ Optional dependencies missing: {', '.join(optional_deps)}")
        print("   These are needed for Google Translate functionality.")
        print("   You can install them later if needed.")
        print("   For now, use DeepL or BhashaIndia translation services.")
    
    try:
        from math_translation_pipeline import main as run_app
        print("✅ All dependencies found")
        print("🎭 Launching Mathematical Expression Translation Pipeline...")
        run_app()
    except ImportError as e:
        print(f"❌ Error importing application: {e}")
        print("Make sure math_translation_pipeline.py is in the same directory")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
