# Quick Start Guide
## Mathematical Expression Translation Pipeline

### 🚀 Getting Started in 5 Minutes

#### Step 1: Launch the Application
```bash
python launch_pipeline.py
```

#### Step 2: Set Up Your Project
1. **Project Setup Tab** (📋):
   - Enter a project name (e.g., "my_math_translation")
   - Browse and select your Excel/CSV file
   - Choose source language (e.g., "English")
   - Choose target language (e.g., "Urdu")
   - Select translation service (DeepL recommended)

#### Step 3: Configure Translation Service

**For DeepL (Recommended)**:
1. Go to https://www.deepl.com/pro-api
2. Sign up for free account (500,000 characters/month free)
3. Copy your API key
4. Paste it in the "DeepL API Key" field

**For BhashaIndia (Best for Indian Languages)**:
1. Go to https://bhashaindia.com/api
2. Sign up for an account
3. Get your API key
4. Paste it in the "BhashaIndia API Key" field
5. Perfect for: Hindi, Urdu, Bengali, Tamil, Telugu, Malayalam, Kannada, Gujarati, Punjabi, Odia, Assamese, Marathi

**For Google Translate**:
- Select "Google Translate" radio button
- No API key needed (but lower quality)

#### Step 4: Run the Pipeline
Click **"🚀 Start Pipeline"** for automatic processing, or run each phase manually:

1. **🎭 Phase 1: Masking** - Replaces math expressions with tokens
2. **🌐 Phase 2: Translation** - Translates text while preserving tokens
3. **🔁 Phase 3: Restoration** - Restores original math expressions

### 📁 Input File Format

Your Excel/CSV file should have these columns:
- `Question` - Mathematical questions
- `Answer_key` - Answer keys with math expressions
- `Solution` - Detailed solutions with math expressions

**Example**:
```csv
Question ID,Question,Answer_key,Solution
1,"Find ∫x² dx","x³/3 + C","Using power rule: ∫x² dx = x³/3 + C"
2,"What is \frac{a}{b}?","Fraction","This represents division of a by b"
```

### 📊 Output Files

After completion, you'll get:
- `project_name_FINAL.csv` - **Main result** with translated content
- `project_name_comparison.html` - Visual comparison report
- `project_name_mapping.csv` - Token mappings (for reference)

### 🎯 Example Workflow

**Input (English)**:
```
Question: Find the derivative of f(x) = \frac{x^2 + 1}{x - 1}
```

**After Masking**:
```
Question: Find the derivative of f(x) = [MATH_001]
Mapping: [MATH_001] → \frac{x^2 + 1}{x - 1}
```

**After Translation (to Urdu)**:
```
Question: f(x) = [MATH_001] کا مشتق تلاش کریں
```

**After Restoration (Final Result)**:
```
Question: f(x) = \frac{x^2 + 1}{x - 1} کا مشتق تلاش کریں
```

### ⚡ Pro Tips

1. **Start Small**: Test with 5-10 rows first
2. **Choose Right Service**:
   - DeepL for European languages
   - BhashaIndia for Indian languages
   - Google Translate for quick/free testing
3. **Batch Size**: Reduce batch size if you get API errors
4. **Backup**: The tool automatically creates backups
5. **Validation**: Check the comparison report for quality

### 🐛 Common Issues & Solutions

**"API Key Invalid"**
- Double-check your DeepL API key
- Make sure you have free quota remaining

**"Translation Too Slow"**
- Reduce batch size to 5-10
- Increase delay between requests

**"Some Math Not Restored"**
- This is normal for very complex expressions
- 95%+ restoration rate is typical

**"File Not Found"**
- Make sure your input file path is correct
- Check that the file isn't open in Excel

### 🎉 Success Indicators

✅ **Masking Success**: See tokens like `[MATH_001]` in results
✅ **Translation Success**: Text changes language, tokens stay same
✅ **Restoration Success**: Math expressions return, text stays translated

### 📞 Need Help?

1. Check the **Settings** tab for API configuration
2. Use **"📋 Test API Connection"** to verify setup
3. Review error messages in the application logs
4. Try with a smaller test file first

---

**🎯 Goal**: Translate educational content while perfectly preserving mathematical expressions!

**⏱️ Time**: 5-15 minutes for typical datasets (100-500 rows)

**💰 Cost**: Free with DeepL's free tier (500K characters/month)
