#!/usr/bin/env python3
"""
Test LaTeX Text Extraction and Translation
==========================================

Test the new approach that properly distinguishes between LaTeX code 
and translatable text content.
"""

import re

def test_latex_text_extraction():
    """Test the LaTeX text extraction functionality"""
    print("🧪 Testing LaTeX Text Extraction...")
    
    # Your exact example
    test_input = r"\text{Find the limit of each of the sequences } (a_n) \text{ in the following cases:} a_n = \frac{1}{2^n}"
    
    print(f"Input: {test_input}")
    
    # Step 1: Extract translatable text from \text{...} commands
    def extract_translatable_text(text):
        text_mapping = {}
        text_counter = 1
        processed_text = text
        
        # Pattern to match \text{content} commands
        text_pattern = r'\\text\{([^}]*)\}'
        
        text_commands = []
        for match in re.finditer(text_pattern, text):
            full_command = match.group(0)  # \text{content}
            content = match.group(1)       # content
            
            if content.strip():  # Only process non-empty content
                text_token = f'[TEXT_{text_counter:03d}]'
                text_mapping[text_token] = {
                    'original_command': full_command,
                    'translatable_content': content,
                    'position': match.start()
                }
                text_commands.append((match.start(), match.end(), full_command, text_token))
                text_counter += 1
        
        # Replace \text{...} commands with text tokens (right to left)
        for start, end, full_command, text_token in reversed(text_commands):
            processed_text = processed_text[:start] + text_token + processed_text[end:]
        
        return processed_text, text_mapping
    
    processed_text, text_mapping = extract_translatable_text(test_input)
    
    print(f"Processed: {processed_text}")
    print(f"Text Mapping: {text_mapping}")
    
    # Step 2: Mask remaining mathematical expressions
    def mask_math_expressions(text):
        math_mapping = {}
        masked_text = text
        counter = 1
        
        # Mathematical patterns (excluding \text which is already handled)
        patterns = [
            (r'\\frac\{[^}]+\}\{[^}]+\}', 'LaTeX fraction'),
            (r'[a-zA-Z]+_[a-zA-Z0-9]+', 'Variable with subscript'),
            (r'[a-zA-Z0-9]+\^[a-zA-Z0-9]+', 'Variable with superscript'),
        ]
        
        for pattern, desc in patterns:
            matches = list(re.finditer(pattern, masked_text))
            for match in reversed(matches):
                if not ('[' in match.group() and ']' in match.group()):
                    token = f'[MATH_{counter:03d}]'
                    math_mapping[token] = {
                        'original': match.group(),
                        'description': desc
                    }
                    masked_text = masked_text[:match.start()] + token + masked_text[match.end():]
                    counter += 1
        
        return masked_text, math_mapping
    
    masked_text, math_mapping = mask_math_expressions(processed_text)
    
    print(f"Masked: {masked_text}")
    print(f"Math Mapping: {math_mapping}")
    
    # Step 3: Simulate translation of TEXT tokens
    def translate_text_tokens(text, text_mapping):
        translated_text = text
        
        for token, info in text_mapping.items():
            if 'translatable_content' in info:
                content = info['translatable_content']
                
                # Simulate translation (English to Indonesian)
                translations = {
                    'Find the limit of each of the sequences ': 'Temukan batas dari setiap urutan ',
                    ' in the following cases:': ' dalam kasus berikut:'
                }
                
                translated_content = translations.get(content, content)
                translated_command = f'\\text{{{translated_content}}}'
                
                translated_text = translated_text.replace(token, translated_command)
                print(f"   Translated: {content} → {translated_content}")
        
        return translated_text
    
    translated_text = translate_text_tokens(masked_text, text_mapping)
    
    print(f"Translated: {translated_text}")
    
    # Step 4: Restore mathematical expressions
    def restore_math_expressions(text, math_mapping):
        restored_text = text
        
        for token, info in math_mapping.items():
            if token in restored_text:
                restored_text = restored_text.replace(token, info['original'])
                print(f"   Restored: {token} → {info['original']}")
        
        return restored_text
    
    final_text = restore_math_expressions(translated_text, math_mapping)
    
    print(f"Final: {final_text}")
    
    # Verify success
    success_criteria = [
        '\\text{Temukan batas dari setiap urutan }' in final_text,
        '\\text{ dalam kasus berikut:}' in final_text,
        '\\frac{1}{2^n}' in final_text,
        'a_n' in final_text,
        '[TEXT_' not in final_text,
        '[MATH_' not in final_text
    ]
    
    success = all(success_criteria)
    
    if success:
        print("✅ SUCCESS: LaTeX text extraction and translation working correctly!")
        print("   ✅ Translatable text extracted and translated")
        print("   ✅ Mathematical expressions preserved")
        print("   ✅ LaTeX structure maintained")
        return True
    else:
        print("❌ FAILED: Issues with LaTeX text extraction")
        for i, criterion in enumerate(success_criteria):
            print(f"   {'✅' if criterion else '❌'} Criterion {i+1}")
        return False

def test_complex_latex():
    """Test with more complex LaTeX expressions"""
    print("\n🧪 Testing Complex LaTeX...")
    
    complex_input = r"\text{Calculate } \int_{0}^{\infty} e^{-x^2} dx \text{ and find } \lim_{n \to \infty} \sum_{k=1}^{n} \frac{1}{k^2}"
    
    print(f"Complex Input: {complex_input}")
    
    # Extract text
    text_pattern = r'\\text\{([^}]*)\}'
    text_parts = re.findall(text_pattern, complex_input)
    
    print("Extractable text parts:")
    for i, part in enumerate(text_parts):
        print(f"   {i+1}. '{part}'")
    
    # Check that mathematical expressions are preserved
    math_expressions = [
        r'\int_{0}^{\infty} e^{-x^2} dx',
        r'\lim_{n \to \infty} \sum_{k=1}^{n} \frac{1}{k^2}'
    ]
    
    print("Mathematical expressions to preserve:")
    for expr in math_expressions:
        print(f"   • {expr}")
    
    return len(text_parts) == 2  # Should find 2 translatable text parts

def main():
    """Run all LaTeX text extraction tests"""
    print("🧪 LaTeX Text Extraction and Translation Tests")
    print("=" * 60)
    
    all_tests_passed = True
    
    # Test 1: Basic extraction and translation
    if not test_latex_text_extraction():
        all_tests_passed = False
    
    # Test 2: Complex LaTeX
    if not test_complex_latex():
        all_tests_passed = False
    
    print("\n" + "=" * 60)
    
    if all_tests_passed:
        print("🎉 ALL LATEX TEXT EXTRACTION TESTS PASSED!")
        print("✅ The system can now properly distinguish between:")
        print("   • LaTeX code to preserve (\\frac{1}{2^n}, a_n, etc.)")
        print("   • Natural language to translate ('Find the limit...', etc.)")
        print("✅ This should fix your original issue!")
    else:
        print("❌ SOME TESTS FAILED!")
        print("⚠️ Additional work may be needed.")
    
    return all_tests_passed

if __name__ == "__main__":
    main()
