import pandas as pd

print("=== CHECKING TEST FILES ===")

# Check CSV
print("CSV file:")
df_csv = pd.read_csv("test_solutions.csv")
for i, row in df_csv.iterrows():
    row_num = i + 12
    solution = row["Masked Solution"]
    print(f"  Row {row_num}: Type={type(solution)}, IsNaN={pd.isna(solution)}")

print("\nXLSXWriter Excel file:")
df_xlsx1 = pd.read_excel("test_xlsxwriter.xlsx")
for i, row in df_xlsx1.iterrows():
    row_num = i + 12
    solution = row["Masked Solution"]
    print(f"  Row {row_num}: Type={type(solution)}, IsNaN={pd.isna(solution)}")

print("\nOpenpyxl Excel file:")
df_xlsx2 = pd.read_excel("test_openpyxl.xlsx")
for i, row in df_xlsx2.iterrows():
    row_num = i + 12
    solution = row["Masked Solution"]
    print(f"  Row {row_num}: Type={type(solution)}, IsNaN={pd.isna(solution)}")
