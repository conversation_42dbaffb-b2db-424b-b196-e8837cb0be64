#!/usr/bin/env python3
"""
Test Masking Fix
===============

Test the masking functionality to ensure it works correctly with the new structure.
"""

import pandas as pd
import re
import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_latex_text_extraction():
    """Test the LaTeX text extraction function"""
    print("🧪 Testing LaTeX Text Extraction...")
    
    def extract_translatable_text_from_latex(text):
        """Extract translatable text from LaTeX commands while preserving structure"""
        if not isinstance(text, str) or pd.isna(text):
            return text, {}, {}
            
        # Find all \text{...} commands and extract their content for translation
        text_commands = []
        text_mapping = {}
        text_counter = 1
        
        # Pattern to match \text{content} commands
        text_pattern = r'\\text\{([^}]*)\}'
        
        for match in re.finditer(text_pattern, text):
            full_command = match.group(0)  # \text{content}
            content = match.group(1)       # content
            
            if content.strip():  # Only process non-empty content
                text_token = f'[TEXT_{text_counter:03d}]'
                text_mapping[text_token] = {
                    'original_command': full_command,
                    'translatable_content': content,
                    'position': match.start()
                }
                text_commands.append((match.start(), match.end(), full_command, text_token))
                text_counter += 1
        
        # Replace \text{...} commands with text tokens (right to left)
        processed_text = text
        for start, end, full_command, text_token in reversed(text_commands):
            processed_text = processed_text[:start] + text_token + processed_text[end:]
        
        return processed_text, text_mapping, {}
    
    # Test with your example
    test_input = r"\text{Find the limit of each of the sequences } (a_n) \text{ in the following cases:} a_n = \frac{1}{2^n}"
    
    processed_text, text_mapping, _ = extract_translatable_text_from_latex(test_input)
    
    print(f"Input: {test_input}")
    print(f"Processed: {processed_text}")
    print(f"Text Mapping: {text_mapping}")
    
    # Verify structure
    success = True
    for token, info in text_mapping.items():
        if 'original_command' not in info:
            print(f"❌ Missing 'original_command' in {token}")
            success = False
        if 'translatable_content' not in info:
            print(f"❌ Missing 'translatable_content' in {token}")
            success = False
        if 'position' not in info:
            print(f"❌ Missing 'position' in {token}")
            success = False
    
    if success:
        print("✅ LaTeX text extraction structure is correct")
    
    return success, text_mapping

def test_math_masking():
    """Test mathematical expression masking"""
    print("\n🧪 Testing Mathematical Expression Masking...")
    
    def mask_math_expressions(text):
        """Simple math masking for testing"""
        local_mapping = {}
        masked_text = text
        token_counter = 1
        
        patterns = [
            (r'\\frac\{[^{}]*\}\{[^{}]*\}', 'LaTeX fraction'),
            (r'[a-zA-Z]_[a-zA-Z0-9]+', 'Variable with subscript'),
        ]
        
        for pattern, description in patterns:
            matches = []
            for match in re.finditer(pattern, masked_text):
                match_text = match.group()
                if not (match_text.startswith('[') and match_text.endswith(']')):
                    matches.append((match.start(), match.end(), match_text, description))
            
            for start, end, match_text, desc in reversed(matches):
                token = f'[MATH_{token_counter:03d}]'
                local_mapping[token] = {
                    'original': match_text,
                    'description': desc,
                    'position': start
                }
                masked_text = masked_text[:start] + token + masked_text[end:]
                token_counter += 1
        
        return masked_text, local_mapping
    
    # Test with processed text from previous step
    test_input = "[TEXT_001] (a_n) [TEXT_002] a_n = \\frac{1}{2^n}"
    
    masked_text, math_mapping = mask_math_expressions(test_input)
    
    print(f"Input: {test_input}")
    print(f"Masked: {masked_text}")
    print(f"Math Mapping: {math_mapping}")
    
    # Verify structure
    success = True
    for token, info in math_mapping.items():
        if 'original' not in info:
            print(f"❌ Missing 'original' in {token}")
            success = False
        if 'description' not in info:
            print(f"❌ Missing 'description' in {token}")
            success = False
        if 'position' not in info:
            print(f"❌ Missing 'position' in {token}")
            success = False
    
    if success:
        print("✅ Mathematical expression masking structure is correct")
    
    return success, math_mapping

def test_combined_mapping():
    """Test the combined mapping structure"""
    print("\n🧪 Testing Combined Mapping Structure...")
    
    # Get mappings from previous tests
    _, text_mapping = test_latex_text_extraction()
    _, math_mapping = test_math_masking()
    
    # Combine mappings
    combined_mapping = {**text_mapping, **math_mapping}
    
    print(f"Combined Mapping: {combined_mapping}")
    
    # Test mapping data creation (like in the actual code)
    mapping_data = []
    for token, info in combined_mapping.items():
        try:
            # Handle different mapping structures (TEXT tokens vs MATH tokens)
            if 'original' in info:
                # Standard MATH token
                original_expr = info['original']
                description = info['description']
                position = info.get('position', 0)
            elif 'original_command' in info:
                # TEXT token with LaTeX command
                original_expr = info['original_command']
                description = f"LaTeX text: {info.get('translatable_content', '')}"
                position = info.get('position', 0)
            else:
                # Fallback for unexpected structure
                original_expr = str(info)
                description = 'Unknown'
                position = 0
            
            mapping_data.append({
                'Token': token,
                'Original_Expression': original_expr,
                'Description': description,
                'Position': position
            })
            
            print(f"✅ Successfully processed {token}")
            
        except Exception as e:
            print(f"❌ Error processing {token}: {e}")
            return False
    
    print(f"✅ Successfully created mapping data with {len(mapping_data)} entries")
    
    # Test DataFrame creation
    try:
        df_mapping = pd.DataFrame(mapping_data)
        print(f"✅ Successfully created DataFrame with columns: {list(df_mapping.columns)}")
        print(f"   Sample data:\n{df_mapping.head()}")
        return True
    except Exception as e:
        print(f"❌ Error creating DataFrame: {e}")
        return False

def main():
    """Run all masking tests"""
    print("🧪 Masking Fix Tests")
    print("=" * 50)
    
    all_tests_passed = True
    
    # Test combined mapping structure
    if not test_combined_mapping():
        all_tests_passed = False
    
    print("\n" + "=" * 50)
    
    if all_tests_passed:
        print("🎉 ALL MASKING TESTS PASSED!")
        print("✅ The masking phase should now work correctly.")
        print("✅ Mapping table will be created successfully.")
    else:
        print("❌ SOME MASKING TESTS FAILED!")
        print("⚠️ Additional debugging may be needed.")
    
    return all_tests_passed

if __name__ == "__main__":
    main()
