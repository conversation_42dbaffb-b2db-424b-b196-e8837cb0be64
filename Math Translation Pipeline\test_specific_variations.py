#!/usr/bin/env python3
"""
Test Specific Placeholder Variations
===================================

Test the exact placeholder variations that were not being restored.
"""

import re

def test_specific_variations():
    """Test the specific variations from the user's error log"""
    print("🧪 Testing Specific Placeholder Variations...")
    
    # Test cases based on the user's error log
    test_cases = [
        {
            'original_placeholder': 'MATHTOKEN0000PLACEHOLDER',
            'modified_placeholder': 'MathToken0000PlaceHolder',
            'expected_token': '[MATH_001]'
        },
        {
            'original_placeholder': 'MATHTOKEN0001PLACEHOLDER', 
            'modified_placeholder': 'MathToken0001PlaceHolder',
            'expected_token': '[MATH_002]'
        },
        {
            'original_placeholder': 'MATHTOKEN0002PLACEHOLDER',
            'modified_placeholder': 'MathToken0002PlaceHolder', 
            'expected_token': '[MATH_003]'
        }
    ]
    
    # Create token map
    token_map = {}
    for i, case in enumerate(test_cases):
        token_map[case['original_placeholder']] = case['expected_token']
    
    print(f"Token Map: {token_map}")
    
    # Test text with modified placeholders (what translation service produces)
    test_text = "Find the limit MathToken0000PlaceHolder when MathToken0001PlaceHolder approaches MathToken0002PlaceHolder"
    print(f"Input Text: {test_text}")
    
    # Enhanced restoration function (based on the fix)
    def enhanced_restore_tokens(text, token_map):
        """Enhanced token restoration with regex pattern matching"""
        if not isinstance(text, str) or not token_map:
            return text

        restored_text = text
        
        # Use regex-based approach for more robust matching
        for original_placeholder, original_token in token_map.items():
            # Extract the number from the original placeholder
            number_match = re.search(r'(\d+)', original_placeholder)
            if not number_match:
                continue
                
            token_number = number_match.group(1)
            
            # Create comprehensive regex pattern to match all variations
            pattern = rf'(?i)(?:MATH|math)(?:TOKEN|token|Token)(?:\s*{token_number}\s*)(?:PLACE|place|Place)(?:HOLDER|holder|Holder)'
            
            # Find all matches and replace them
            matches = list(re.finditer(pattern, restored_text))
            for match in reversed(matches):  # Replace from right to left
                print(f"   Restoring: {match.group()} → {original_token}")
                restored_text = restored_text[:match.start()] + original_token + restored_text[match.end():]

        return restored_text
    
    # Test the restoration
    restored_text = enhanced_restore_tokens(test_text, token_map)
    print(f"Restored Text: {restored_text}")
    
    # Verify success
    success = True
    for case in test_cases:
        if case['modified_placeholder'] in restored_text:
            print(f"❌ Failed to restore: {case['modified_placeholder']}")
            success = False
        elif case['expected_token'] in restored_text:
            print(f"✅ Successfully restored: {case['modified_placeholder']} → {case['expected_token']}")
        else:
            print(f"⚠️ Unexpected result for: {case['modified_placeholder']}")
            success = False
    
    return success

def test_regex_patterns():
    """Test the regex patterns work correctly"""
    print("\n🔍 Testing Regex Patterns...")
    
    # Test various placeholder formats
    test_patterns = [
        'MATHTOKEN0000PLACEHOLDER',
        'MathToken0000PlaceHolder', 
        'mathtoken0000placeholder',
        'MATHTOKEN 0000 PLACEHOLDER',
        'MathToken 0000 PlaceHolder',
        'mathToken0000placeHolder',
        'MATHTOKEN0123PLACEHOLDER',
        'MathToken9999PlaceHolder'
    ]
    
    # The regex pattern from our fix
    base_pattern = r'(?i)(?:MATH|math)(?:TOKEN|token|Token)(?:\s*(\d+)\s*)(?:PLACE|place|Place)(?:HOLDER|holder|Holder)'
    
    print("Testing pattern matching:")
    for test_pattern in test_patterns:
        match = re.search(base_pattern, test_pattern)
        if match:
            number = match.group(1)
            print(f"   ✅ {test_pattern} → Number: {number}")
        else:
            print(f"   ❌ {test_pattern} → No match")
    
    return True

def test_complete_restoration_chain():
    """Test the complete restoration chain"""
    print("\n🔗 Testing Complete Restoration Chain...")
    
    # Original mathematical expressions
    original_expressions = {
        '[MATH_001]': r'\text{Find the limit}',
        '[MATH_002]': r'a_n',
        '[MATH_003]': r'\frac{1}{2^n}'
    }
    
    # Simulate the complete chain
    text = "Calculate [MATH_001] of [MATH_002] = [MATH_003]"
    print(f"1. Original: {text}")
    
    # Step 1: Protection (create placeholders)
    token_map = {}
    protected_text = text
    for i, token in enumerate(['[MATH_001]', '[MATH_002]', '[MATH_003]']):
        placeholder = f'MATHTOKEN{i:04d}PLACEHOLDER'
        token_map[placeholder] = token
        protected_text = protected_text.replace(token, placeholder)
    
    print(f"2. Protected: {protected_text}")
    
    # Step 2: Translation modification
    modified_text = protected_text.replace('MATHTOKEN', 'MathToken').replace('PLACEHOLDER', 'PlaceHolder')
    print(f"3. Modified: {modified_text}")
    
    # Step 3: Enhanced restoration
    def complete_restore(text, token_map, expression_map):
        # First restore placeholders to tokens
        for original_placeholder, token in token_map.items():
            number_match = re.search(r'(\d+)', original_placeholder)
            if number_match:
                token_number = number_match.group(1)
                pattern = rf'(?i)(?:MATH|math)(?:TOKEN|token|Token)(?:\s*{token_number}\s*)(?:PLACE|place|Place)(?:HOLDER|holder|Holder)'
                text = re.sub(pattern, token, text)
        
        # Then restore tokens to expressions
        for token, expression in expression_map.items():
            text = text.replace(token, expression)
        
        return text
    
    final_text = complete_restore(modified_text, token_map, original_expressions)
    print(f"4. Final: {final_text}")
    
    # Check success
    success = all(expr in final_text for expr in original_expressions.values())
    if success:
        print("✅ Complete restoration chain successful!")
    else:
        print("❌ Complete restoration chain failed!")
    
    return success

def main():
    """Run all tests"""
    print("🧪 Specific Placeholder Variation Tests")
    print("=" * 60)
    
    all_tests_passed = True
    
    # Test 1: Specific variations
    if not test_specific_variations():
        all_tests_passed = False
    
    # Test 2: Regex patterns
    if not test_regex_patterns():
        all_tests_passed = False
    
    # Test 3: Complete chain
    if not test_complete_restoration_chain():
        all_tests_passed = False
    
    print("\n" + "=" * 60)
    
    if all_tests_passed:
        print("🎉 ALL SPECIFIC VARIATION TESTS PASSED!")
        print("✅ The placeholder restoration fix should work for your case.")
        print("✅ MathToken0000PlaceHolder variations will be properly restored.")
    else:
        print("❌ SOME TESTS FAILED!")
        print("⚠️ Additional debugging may be needed.")
    
    return all_tests_passed

if __name__ == "__main__":
    main()
