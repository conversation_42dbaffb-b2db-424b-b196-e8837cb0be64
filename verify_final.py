import pandas as pd

# Load the final file
df_masked = pd.read_excel("masked_GPT_TEST_FINAL.xlsx", sheet_name="Masked Dataset")

print("=== FINAL VERIFICATION ===")
print("Rows 12-16 from FINAL Masked Dataset:")
for i in range(11, min(16, len(df_masked))):
    row_num = i + 1
    solution = df_masked.iloc[i]['Masked Solution']
    print(f"Row {row_num}:")
    print(f"  Type: {type(solution)}")
    print(f"  Is NaN: {pd.isna(solution)}")
    if not pd.isna(solution):
        if isinstance(solution, str):
            print(f"  Content (first 100 chars): {repr(solution[:100])}")
        else:
            print(f"  Content (converted to): {repr(solution)}")
    else:
        print(f"  Content: NaN")
    print()

# Check mapping table
df_mapping = pd.read_excel("masked_GPT_TEST_FINAL.xlsx", sheet_name="Mapping Table")
print(f"Mapping table contains {len(df_mapping)} entries")
print("First 5 mappings:")
print(df_mapping.head())

# Summary
total_rows = len(df_masked)
empty_solutions = df_masked['Masked Solution'].isna().sum()
non_empty_solutions = total_rows - empty_solutions

print(f"\n=== SUMMARY ===")
print(f"Total rows: {total_rows}")
print(f"Empty masked solutions: {empty_solutions}")
print(f"Non-empty masked solutions: {non_empty_solutions}")
print(f"Success rate: {(non_empty_solutions/total_rows)*100:.1f}%")

if empty_solutions == 0:
    print("🎉 SUCCESS: All Solution cells have been properly masked!")
else:
    print(f"⚠️  WARNING: {empty_solutions} Solution cells are still empty")
