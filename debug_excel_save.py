import pandas as pd
import re

# Load your Excel file
input_file = "GPT TEST.xlsx"
df = pd.read_excel(input_file, sheet_name="Sheet1")

# Mapping dictionary
mapping = {}
token_counters = {"M": 1, "A": 1, "S": 1}

def mask_text(text, prefix):
    """Mask LaTeX/math expressions in text with tokens"""
    if not isinstance(text, str):
        return text

    # Store original text for reference
    original_text = text
    masked_text = text

    # Define patterns in order of priority (most specific first)
    patterns = [
        # Complete integral expressions (highest priority)
        r"∫[^∫\n]*?(?:dx|dy|dz|dt|dθ|du|dv|dw)",

        # LaTeX commands with their complete arguments
        r"\\int\\frac\{[^}]*\}\{[^}]*\}d\\theta",
        r"\\frac\{[^}]*\}\{[^}]*\}",
        r"\\sqrt\{[^}]*\}",
        r"\\[a-zA-Z]+\{[^}]*\}",
        r"\\[a-zA-Z]+",

        # Mathematical expressions with superscripts/subscripts
        r"[a-zA-Z0-9]+\^[a-zA-Z0-9⁄()]+",
        r"[a-zA-Z0-9]+_[a-zA-Z0-9⁄()]+",
        r"[a-zA-Z0-9]+[²³⁴⁵⁶⁷⁸⁹⁰¹]+",
        r"[a-zA-Z0-9]+[₀₁₂₃₄₅₆₇₈₉]+",

        # Fractions
        r"[0-9]+⁄[0-9]+",

        # Square roots with Unicode
        r"√[a-zA-Z0-9]+",

        # Mathematical symbols and special characters
        r"[ℝ∞±∉]",
    ]

    # Apply patterns one by one, being careful not to re-process already masked content
    for pattern in patterns:
        # Find all matches in the current state of masked_text
        matches = []
        for match in re.finditer(pattern, masked_text):
            match_text = match.group()
            # Skip if this is already a token
            if not (match_text.startswith('<') and match_text.endswith('>')):
                matches.append((match.start(), match.end(), match_text))

        # Replace matches from right to left to preserve positions
        for start, end, match_text in reversed(matches):
            token = f"<{prefix}{token_counters[prefix]}>"
            mapping[token] = match_text
            masked_text = masked_text[:start] + token + masked_text[end:]
            token_counters[prefix] += 1

    return masked_text

# Create a copy of the original dataframe
df_result = df.copy()

# Process Solution column only for debugging
print("Processing Solution column...")
masked_solutions = []
for i, solution in enumerate(df["Solution"]):
    if pd.isna(solution):
        masked_solutions.append(solution)
    else:
        masked = mask_text(solution, "S")
        masked_solutions.append(masked)
        if i >= 11 and i <= 15:  # rows 12-16
            print(f"  Row {i+1}: Original type={type(solution)}")
            print(f"  Row {i+1}: Masked type={type(masked)}")
            print(f"  Row {i+1}: Masked content={repr(masked[:100])}")

df_result["Masked Solution"] = masked_solutions

print("\nChecking DataFrame before saving...")
for i in range(11, min(16, len(df_result))):
    row_num = i + 1
    solution = df_result.iloc[i]["Masked Solution"]
    print(f"Row {row_num}: Type={type(solution)}, IsNaN={pd.isna(solution)}")
    if not pd.isna(solution):
        print(f"  Content: {repr(solution[:50])}")

# Try different saving approaches
print("\n=== TESTING DIFFERENT SAVE METHODS ===")

# Method 1: Save just the solution column as CSV
print("Method 1: Saving as CSV...")
df_test = df_result[["Question ID", "Masked Solution"]].iloc[11:16]
df_test.to_csv("test_solutions.csv", index=False)

# Method 2: Save with different Excel engines
print("Method 2: Saving with xlsxwriter...")
try:
    with pd.ExcelWriter("test_xlsxwriter.xlsx", engine="xlsxwriter") as writer:
        df_test.to_excel(writer, index=False, sheet_name="Test")
    print("  xlsxwriter: SUCCESS")
except Exception as e:
    print(f"  xlsxwriter: ERROR - {e}")

print("Method 3: Saving with openpyxl...")
try:
    with pd.ExcelWriter("test_openpyxl.xlsx", engine="openpyxl") as writer:
        df_test.to_excel(writer, index=False, sheet_name="Test")
    print("  openpyxl: SUCCESS")
except Exception as e:
    print(f"  openpyxl: ERROR - {e}")

# Method 4: Check for problematic characters
print("\nMethod 4: Checking for problematic characters...")
for i in range(11, min(16, len(df_result))):
    row_num = i + 1
    solution = df_result.iloc[i]["Masked Solution"]
    if not pd.isna(solution):
        # Check for null bytes or other problematic characters
        if '\x00' in solution:
            print(f"  Row {row_num}: Contains null bytes!")
        if any(ord(c) > 127 for c in solution):
            print(f"  Row {row_num}: Contains non-ASCII characters")
        # Check string length
        print(f"  Row {row_num}: Length = {len(solution)}")

print("\nReading back the CSV to verify...")
df_csv = pd.read_csv("test_solutions.csv")
for i, row in df_csv.iterrows():
    row_num = i + 12  # since we started from row 11 (0-indexed)
    solution = row["Masked Solution"]
    print(f"CSV Row {row_num}: Type={type(solution)}, IsNaN={pd.isna(solution)}")
    if not pd.isna(solution):
        print(f"  Content: {repr(solution[:50])}")
