import pandas as pd

# Load the fresh file
df_masked = pd.read_excel("masked_GPT_TEST_fresh.xlsx", sheet_name="Masked Dataset")

print("=== VERIFICATION OF FRESH FILE ===")
print("Rows 12-16 from Fresh Masked Dataset:")
for i in range(11, min(16, len(df_masked))):
    row_num = i + 1
    solution = df_masked.iloc[i]['Masked Solution']
    print(f"Row {row_num}:")
    print(f"  Type: {type(solution)}")
    print(f"  Is NaN: {pd.isna(solution)}")
    if not pd.isna(solution):
        print(f"  Content (first 100 chars): {repr(solution[:100])}")
    else:
        print(f"  Content: NaN")
    print()
