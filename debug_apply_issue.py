import pandas as pd
import re

# Load the original data
df = pd.read_excel("GPT TEST.xlsx")

# Global mapping and counters
mapping = {}
token_counters = {"M": 1, "A": 1, "S": 1}

def mask_text(text, prefix):
    """Mask LaTeX/math expressions in text with tokens"""
    if not isinstance(text, str):
        return text

    # Store original text for reference
    original_text = text
    masked_text = text

    # Define patterns in order of priority (most specific first)
    patterns = [
        # Complete integral expressions (highest priority)
        r"∫[^∫\n]*?(?:dx|dy|dz|dt|dθ|du|dv|dw)",

        # LaTeX commands with their complete arguments
        r"\\int\\frac\{[^}]*\}\{[^}]*\}d\\theta",
        r"\\frac\{[^}]*\}\{[^}]*\}",
        r"\\sqrt\{[^}]*\}",
        r"\\[a-zA-Z]+\{[^}]*\}",
        r"\\[a-zA-Z]+",

        # Mathematical expressions with superscripts/subscripts
        r"[a-zA-Z0-9]+\^[a-zA-Z0-9⁄()]+",
        r"[a-zA-Z0-9]+_[a-zA-Z0-9⁄()]+",
        r"[a-zA-Z0-9]+[²³⁴⁵⁶⁷⁸⁹⁰¹]+",
        r"[a-zA-Z0-9]+[₀₁₂₃₄₅₆₇₈₉]+",

        # Fractions
        r"[0-9]+⁄[0-9]+",

        # Square roots with Unicode
        r"√[a-zA-Z0-9]+",

        # Mathematical symbols and special characters
        r"[ℝ∞±∉]",
    ]

    # Apply patterns one by one, being careful not to re-process already masked content
    for pattern in patterns:
        # Find all matches in the current state of masked_text
        matches = []
        for match in re.finditer(pattern, masked_text):
            match_text = match.group()
            # Skip if this is already a token
            if not (match_text.startswith('<') and match_text.endswith('>')):
                matches.append((match.start(), match.end(), match_text))

        # Replace matches from right to left to preserve positions
        for start, end, match_text in reversed(matches):
            token = f"<{prefix}{token_counters[prefix]}>"
            mapping[token] = match_text
            masked_text = masked_text[:start] + token + masked_text[end:]
            token_counters[prefix] += 1

    return masked_text

def safe_mask_text(text, prefix):
    """Safely apply masking with explicit NaN handling"""
    if pd.isna(text):
        return text  # Keep NaN as NaN
    return mask_text(text, prefix)

print("=== TESTING PANDAS APPLY ON SOLUTION COLUMN ===")

# Test the apply function step by step
print("Original Solution values for rows 12-16:")
for i in range(11, min(16, len(df))):
    row_num = i + 1
    solution_value = df.iloc[i]['Solution']
    print(f"Row {row_num}: Type={type(solution_value)}, IsNaN={pd.isna(solution_value)}")
    if not pd.isna(solution_value):
        print(f"  Content (first 50 chars): {repr(solution_value[:50])}")

print("\n" + "="*50)
print("Applying masking function...")

# Apply the function manually to each row
manual_results = []
for i in range(11, min(16, len(df))):
    row_num = i + 1
    solution_value = df.iloc[i]['Solution']
    try:
        result = safe_mask_text(solution_value, "S")
        manual_results.append(result)
        print(f"Row {row_num}: SUCCESS - Type={type(result)}, IsNaN={pd.isna(result)}")
        if not pd.isna(result):
            print(f"  Result (first 50 chars): {repr(result[:50])}")
    except Exception as e:
        manual_results.append(None)
        print(f"Row {row_num}: ERROR - {e}")

print("\n" + "="*50)
print("Testing pandas apply on subset...")

# Test pandas apply on just these rows
subset_df = df.iloc[11:16].copy()
try:
    subset_df["Masked Solution"] = subset_df["Solution"].apply(lambda x: safe_mask_text(x, "S"))
    print("Pandas apply SUCCESS!")
    
    for i, (idx, row) in enumerate(subset_df.iterrows()):
        row_num = idx + 1
        masked_solution = row["Masked Solution"]
        print(f"Row {row_num}: Type={type(masked_solution)}, IsNaN={pd.isna(masked_solution)}")
        if not pd.isna(masked_solution):
            print(f"  Result (first 50 chars): {repr(masked_solution[:50])}")
            
except Exception as e:
    print(f"Pandas apply ERROR: {e}")
