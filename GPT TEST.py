import pandas as pd
import re

# Load your Excel file
input_file = "GPT TEST.xlsx"   # <- put your file name here
df = pd.read_excel(input_file, sheet_name="Sheet1")

# Mapping dictionary
mapping = {}
token_counters = {"M": 1, "A": 1, "S": 1}

def mask_text(text, prefix):
    """Mask LaTeX/math expressions in text with tokens"""
    if not isinstance(text, str):
        return text

    # Store original text for reference
    original_text = text
    masked_text = text

    # Define patterns in order of priority (most specific first)
    patterns = [
        # Complete integral expressions (highest priority)
        r"∫[^∫\n]*?(?:dx|dy|dz|dt|dθ|du|dv|dw)",

        # LaTeX commands with their complete arguments
        r"\\int\\frac\{[^}]*\}\{[^}]*\}d\\theta",
        r"\\frac\{[^}]*\}\{[^}]*\}",
        r"\\sqrt\{[^}]*\}",
        r"\\[a-zA-Z]+\{[^}]*\}",
        r"\\[a-zA-Z]+",

        # Mathematical expressions with superscripts/subscripts
        r"[a-zA-Z0-9]+\^[a-zA-Z0-9⁄()]+",
        r"[a-zA-Z0-9]+_[a-zA-Z0-9⁄()]+",
        r"[a-zA-Z0-9]+[²³⁴⁵⁶⁷⁸⁹⁰¹]+",
        r"[a-zA-Z0-9]+[₀₁₂₃₄₅₆₇₈₉]+",

        # Fractions
        r"[0-9]+⁄[0-9]+",

        # Square roots with Unicode
        r"√[a-zA-Z0-9]+",

        # Mathematical symbols and special characters
        r"[ℝ∞±∉]",
    ]

    # Apply patterns one by one, being careful not to re-process already masked content
    for pattern in patterns:
        # Find all matches in the current state of masked_text
        matches = []
        for match in re.finditer(pattern, masked_text):
            match_text = match.group()
            # Skip if this is already a token
            if not (match_text.startswith('<') and match_text.endswith('>')):
                matches.append((match.start(), match.end(), match_text))

        # Replace matches from right to left to preserve positions
        for start, end, match_text in reversed(matches):
            token = f"<{prefix}{token_counters[prefix]}>"
            mapping[token] = match_text
            masked_text = masked_text[:start] + token + masked_text[end:]
            token_counters[prefix] += 1

    return masked_text

# Apply masking to each column with explicit handling of NaN values
def safe_mask_text(text, prefix):
    """Safely apply masking with explicit NaN handling"""
    if pd.isna(text):
        return text  # Keep NaN as NaN
    try:
        result = mask_text(text, prefix)
        return result
    except Exception as e:
        print(f"ERROR masking text with prefix {prefix}: {e}")
        print(f"Text type: {type(text)}, Text content: {repr(text[:100] if isinstance(text, str) else text)}")
        return text  # Return original text if masking fails

print("Masking Question column...")
df["Masked Question"] = df["Question"].apply(lambda x: safe_mask_text(x, "M"))
print("Masking Answer Key column...")
df["Masked Answer Key"] = df["Answer_key"].apply(lambda x: safe_mask_text(x, "A"))
print("Masking Solution column...")
df["Masked Solution"] = df["Solution"].apply(lambda x: safe_mask_text(x, "S"))
print("Masking complete!")

# Prepare masked dataset
df_masked = df[[
    "Question ID", "Subject", "Chapter name", "Topic",
    "Masked Question", "Masked Answer Key", "Masked Solution",
    "Question Image tag / Url", "Solution Image tag / Url"
]]

# Create mapping table
df_mapping = pd.DataFrame(list(mapping.items()), columns=["Token", "Original Math Expression"])

# Save to Excel with two sheets
output_file = "masked_GPT_TEST_debug.xlsx"
with pd.ExcelWriter(output_file, engine="openpyxl") as writer:
    df_masked.to_excel(writer, index=False, sheet_name="Masked Dataset")
    df_mapping.to_excel(writer, index=False, sheet_name="Mapping Table")

print(f"Masked file created: {output_file}")
