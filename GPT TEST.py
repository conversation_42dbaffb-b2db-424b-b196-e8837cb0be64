import pandas as pd
import re

# Load your Excel file
input_file = "GPT TEST.xlsx"   # <- put your file name here
df = pd.read_excel(input_file, sheet_name="Sheet1")

# Mapping dictionary
mapping = {}
token_counters = {"M": 1, "A": 1, "S": 1}

def mask_text(text, prefix):
    """Mask LaTeX/math expressions in text with tokens"""
    if not isinstance(text, str):
        return text
    pattern = r"(\\[a-zA-Z]+(?:_{[^}]+}|\\?[^ \n]*)?|\{[^{}]+\}|\([^\)]+\)|[0-9]+[\^][a-zA-Z0-9]+|[a-zA-Z]+_{[0-9]+}|[a-zA-Z]+\^[0-9]+)"
    masked_text = text
    matches = re.findall(pattern, text)
    for match in matches:
        token = f"<{prefix}{token_counters[prefix]}>"
        mapping[token] = match
        masked_text = masked_text.replace(match, token, 1)
        token_counters[prefix] += 1
    return masked_text

# Apply masking to each column
df["Masked Question"] = df["Question"].apply(lambda x: mask_text(x, "M"))
df["Masked Answer Key"] = df["Answer_key"].apply(lambda x: mask_text(x, "A"))
df["Masked Solution"] = df["Solution"].apply(lambda x: mask_text(x, "S"))

# Prepare masked dataset
df_masked = df[[
    "Question ID", "Subject", "Chapter name", "Topic",
    "Masked Question", "Masked Answer Key", "Masked Solution",
    "Question Image tag / Url", "Solution Image tag / Url"
]]

# Create mapping table
df_mapping = pd.DataFrame(list(mapping.items()), columns=["Token", "Original Math Expression"])

# Save to Excel with two sheets
output_file = "masked_GPT_TEST.xlsx"
with pd.ExcelWriter(output_file, engine="openpyxl") as writer:
    df_masked.to_excel(writer, index=False, sheet_name="Masked Dataset")
    df_mapping.to_excel(writer, index=False, sheet_name="Mapping Table")

print(f"Masked file created: {output_file}")
