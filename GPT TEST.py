import pandas as pd
import re

# Load your Excel file
input_file = "GPT TEST.xlsx"   # <- put your file name here
df = pd.read_excel(input_file, sheet_name="Sheet1")

# Mapping dictionary
mapping = {}
token_counters = {"M": 1, "A": 1, "S": 1}

def mask_text(text, prefix):
    """Mask LaTeX/math expressions in text with tokens"""
    if not isinstance(text, str):
        return text

    # More comprehensive pattern to catch various mathematical expressions
    patterns = [
        # LaTeX expressions
        r"\\[a-zA-Z]+(?:\{[^}]*\}|\[[^\]]*\])*",  # LaTeX commands with arguments
        r"\$[^$]+\$",  # Inline math mode
        r"\$\$[^$]+\$\$",  # Display math mode
        r"\\frac\{[^}]*\}\{[^}]*\}",  # Fractions
        r"\\sqrt\{[^}]*\}",  # Square roots
        r"\\sum_\{[^}]*\}\^\{[^}]*\}",  # Summations
        r"\\int_\{[^}]*\}\^\{[^}]*\}",  # Integrals
        r"\\lim_\{[^}]*\}",  # Limits

        # Unicode mathematical expressions
        r"∫[^∫]*(?:dx|dy|dz|dt|dθ|du|dv|dw)",  # Integrals with differentials
        r"∫[₀-₉⁰-⁹]*[₀-₉⁰-⁹]*[^∫]*(?:dx|dy|dz|dt|dθ|du|dv|dw)",  # Integrals with limits
        r"√[^√\s]*",  # Square roots
        r"[a-zA-Z0-9]+[²³⁴⁵⁶⁷⁸⁹⁰¹]+",  # Superscripts
        r"[a-zA-Z0-9]+[₀₁₂₃₄₅₆₇₈₉]+",  # Subscripts
        r"[0-9]+⁄[0-9]+",  # Fractions with ⁄
        r"[a-zA-Z]+[²³⁴⁵⁶⁷⁸⁹⁰¹⁄₀₁₂₃₄₅₆₇₈₉]+",  # Variables with super/subscripts

        # General patterns
        r"\{[^{}]*\}",  # Curly braces content
        r"\([^)]*\)",  # Parentheses content (if contains math symbols)
        r"\[[^\]]*\]",  # Square brackets content
        r"[a-zA-Z]+_\{[^}]+\}",  # Subscripts with braces
        r"[a-zA-Z]+\^\{[^}]+\}",  # Superscripts with braces
        r"[a-zA-Z]+_[a-zA-Z0-9]+",  # Simple subscripts
        r"[a-zA-Z]+\^[a-zA-Z0-9]+",  # Simple superscripts
        r"[0-9]+\^[a-zA-Z0-9]+",  # Numbers with superscripts
        r"[0-9]+_[a-zA-Z0-9]+",  # Numbers with subscripts

        # Mathematical symbols
        r"[≤≥≠±∞∑∏∫√π∂∇∆Ω∈∉⊂⊃∪∩∧∨¬∀∃θℝ²³⁴⁵⁶⁷⁸⁹⁰¹⁄₀₁₂₃₄₅₆₇₈₉]+",  # Mathematical symbols
    ]

    masked_text = text

    # Apply each pattern
    for pattern in patterns:
        matches = re.findall(pattern, masked_text)
        for match in matches:
            # Skip if already a token
            if match.startswith('<') and match.endswith('>'):
                continue
            token = f"<{prefix}{token_counters[prefix]}>"
            mapping[token] = match
            masked_text = masked_text.replace(match, token, 1)
            token_counters[prefix] += 1

    return masked_text

# Apply masking to each column
df["Masked Question"] = df["Question"].apply(lambda x: mask_text(x, "M"))
df["Masked Answer Key"] = df["Answer_key"].apply(lambda x: mask_text(x, "A"))
df["Masked Solution"] = df["Solution"].apply(lambda x: mask_text(x, "S"))

# Prepare masked dataset
df_masked = df[[
    "Question ID", "Subject", "Chapter name", "Topic",
    "Masked Question", "Masked Answer Key", "Masked Solution",
    "Question Image tag / Url", "Solution Image tag / Url"
]]

# Create mapping table
df_mapping = pd.DataFrame(list(mapping.items()), columns=["Token", "Original Math Expression"])

# Save to Excel with two sheets
output_file = "masked_GPT_TEST_unicode_fixed.xlsx"
with pd.ExcelWriter(output_file, engine="openpyxl") as writer:
    df_masked.to_excel(writer, index=False, sheet_name="Masked Dataset")
    df_mapping.to_excel(writer, index=False, sheet_name="Mapping Table")

print(f"Masked file created: {output_file}")
