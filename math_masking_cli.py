#!/usr/bin/env python3
"""
Mathematical Expression Masking Tool - Command Line Interface
============================================================

Command-line version of the Mathematical Expression Masking Tool.

Usage:
    # Mask expressions
    python math_masking_cli.py mask input.xlsx output_prefix

    # Restore expressions  
    python math_masking_cli.py restore mapping.csv masked_data.csv

Examples:
    python math_masking_cli.py mask "GPT TEST.xlsx" "my_dataset"
    python math_masking_cli.py restore "my_dataset_mapping.csv" "my_dataset_dataset.csv"
"""

import sys
import pandas as pd
import re
from pathlib import Path

class MathMaskingCLI:
    def __init__(self):
        self.mapping = {}
        self.token_counters = {"M": 1, "A": 1, "S": 1}
        
    def clean_for_excel(self, text):
        """Clean text to be Excel-compatible"""
        if not isinstance(text, str):
            return text
        
        replacements = {
            '²': '^2', '³': '^3', '⁴': '^4', '⁵': '^5', '⁶': '^6', '⁷': '^7', '⁸': '^8', '⁹': '^9', '⁰': '^0', '¹': '^1',
            '₀': '_0', '₁': '_1', '₂': '_2', '₃': '_3', '₄': '_4', '₅': '_5', '₆': '_6', '₇': '_7', '₈': '_8', '₉': '_9',
            '⁄': '/', '√': 'sqrt', '∫': 'integral', '∞': 'infinity', '±': '+/-', '∉': 'not_in', 'ℝ': 'R', 'θ': 'theta',
            '½': '1/2', '⅓': '1/3', '¼': '1/4', '¾': '3/4', '→': '->'
        }
        
        cleaned_text = text
        for unicode_char, ascii_replacement in replacements.items():
            cleaned_text = cleaned_text.replace(unicode_char, ascii_replacement)
        
        return cleaned_text

    def mask_text(self, text, prefix):
        """Mask mathematical expressions with tokens"""
        if not isinstance(text, str):
            return text

        masked_text = text
        patterns = [
            r"∫[^∫\n]*?(?:dx|dy|dz|dt|dθ|du|dv|dw)",
            r"\\int\\frac\{[^}]*\}\{[^}]*\}d\\theta",
            r"\\frac\{[^}]*\}\{[^}]*\}",
            r"\\sqrt\{[^}]*\}",
            r"\\[a-zA-Z]+\{[^}]*\}",
            r"\\[a-zA-Z]+",
            r"[a-zA-Z0-9]+\^[a-zA-Z0-9⁄()]+",
            r"[a-zA-Z0-9]+_[a-zA-Z0-9⁄()]+",
            r"[a-zA-Z0-9]+[²³⁴⁵⁶⁷⁸⁹⁰¹]+",
            r"[a-zA-Z0-9]+[₀₁₂₃₄₅₆₇₈₉]+",
            r"[0-9]+⁄[0-9]+",
            r"√[a-zA-Z0-9]+",
            r"[ℝ∞±∉]",
        ]

        for pattern in patterns:
            matches = []
            for match in re.finditer(pattern, masked_text):
                match_text = match.group()
                if not (match_text.startswith('<') and match_text.endswith('>')):
                    matches.append((match.start(), match.end(), match_text))

            for start, end, match_text in reversed(matches):
                token = f"<{prefix}{self.token_counters[prefix]}>"
                self.mapping[token] = match_text
                masked_text = masked_text[:start] + token + masked_text[end:]
                self.token_counters[prefix] += 1

        return self.clean_for_excel(masked_text)
        
    def mask_dataset(self, input_file, output_prefix):
        """Mask mathematical expressions in dataset"""
        print(f"Loading file: {input_file}")
        
        if input_file.endswith('.xlsx'):
            df = pd.read_excel(input_file)
        elif input_file.endswith('.csv'):
            df = pd.read_csv(input_file)
        else:
            raise ValueError("Unsupported file format. Use .xlsx or .csv")
            
        print(f"Loaded {len(df)} rows")
        
        df_result = df.copy()
        
        # Process columns
        if 'Question' in df.columns:
            print("Processing Question column...")
            masked_questions = [self.mask_text(q, "M") if pd.notna(q) else q for q in df["Question"]]
            df_result["Masked Question"] = masked_questions
            
        if 'Answer_key' in df.columns:
            print("Processing Answer Key column...")
            masked_answers = [self.mask_text(a, "A") if pd.notna(a) else a for a in df["Answer_key"]]
            df_result["Masked Answer Key"] = masked_answers
            
        if 'Solution' in df.columns:
            print("Processing Solution column...")
            masked_solutions = [self.mask_text(s, "S") if pd.notna(s) else s for s in df["Solution"]]
            df_result["Masked Solution"] = masked_solutions
            
        # Prepare output
        columns_to_keep = ["Question ID", "Subject", "Chapter name", "Topic", "Question Image tag / Url", "Solution Image tag / Url"]
        available_columns = [col for col in columns_to_keep if col in df_result.columns]
        
        for col in ["Masked Question", "Masked Answer Key", "Masked Solution"]:
            if col in df_result.columns:
                available_columns.append(col)
                
        df_masked = df_result[available_columns]
        df_mapping = pd.DataFrame(list(self.mapping.items()), columns=["Token", "Original Math Expression"])
        
        # Save files
        masked_file = f"{output_prefix}_dataset.csv"
        mapping_file = f"{output_prefix}_mapping.csv"
        
        print(f"Saving masked dataset: {masked_file}")
        df_masked.to_csv(masked_file, index=False)
        
        print(f"Saving mapping table: {mapping_file}")
        df_mapping.to_csv(mapping_file, index=False)
        
        print(f"\n✅ SUCCESS!")
        print(f"📊 Processed {len(df_masked)} rows")
        print(f"🔤 Created {len(self.mapping)} tokens")
        print(f"📁 Files: {masked_file}, {mapping_file}")
        
    def restore_text(self, text, token_mapping):
        """Restore tokens to original expressions"""
        if not isinstance(text, str):
            return text
            
        restored_text = text
        sorted_tokens = sorted(token_mapping.keys(), key=len, reverse=True)
        
        for token in sorted_tokens:
            if token in restored_text:
                restored_text = restored_text.replace(token, token_mapping[token])
                
        return restored_text
        
    def restore_dataset(self, mapping_file, masked_file):
        """Restore original expressions from masked dataset"""
        print(f"Loading mapping table: {mapping_file}")
        df_mapping = pd.read_csv(mapping_file)
        token_mapping = dict(zip(df_mapping['Token'], df_mapping['Original Math Expression']))
        print(f"Loaded {len(token_mapping)} token mappings")
        
        print(f"Loading masked dataset: {masked_file}")
        df_masked = pd.read_csv(masked_file)
        print(f"Loaded {len(df_masked)} rows")
        
        df_restored = df_masked.copy()
        
        # Restore columns
        columns_to_restore = [
            ('Masked Question', 'Restored Question'),
            ('Masked Answer Key', 'Restored Answer Key'),
            ('Masked Solution', 'Restored Solution')
        ]
        
        for masked_col, restored_col in columns_to_restore:
            if masked_col in df_masked.columns:
                print(f"Restoring {masked_col}...")
                restored_data = [self.restore_text(text, token_mapping) if pd.notna(text) else text 
                               for text in df_masked[masked_col]]
                df_restored[restored_col] = restored_data
                
        # Save restored dataset
        base_name = Path(masked_file).stem
        restored_file = f"{base_name}_RESTORED.csv"
        
        print(f"Saving restored dataset: {restored_file}")
        df_restored.to_csv(restored_file, index=False)
        
        print(f"\n✅ SUCCESS!")
        print(f"📊 Processed {len(df_restored)} rows")
        print(f"🔄 Used {len(token_mapping)} token mappings")
        print(f"📁 File: {restored_file}")

def main():
    if len(sys.argv) < 2:
        print(__doc__)
        sys.exit(1)
        
    command = sys.argv[1].lower()
    cli = MathMaskingCLI()
    
    try:
        if command == "mask":
            if len(sys.argv) != 4:
                print("Usage: python math_masking_cli.py mask <input_file> <output_prefix>")
                sys.exit(1)
            input_file, output_prefix = sys.argv[2], sys.argv[3]
            cli.mask_dataset(input_file, output_prefix)
            
        elif command == "restore":
            if len(sys.argv) != 4:
                print("Usage: python math_masking_cli.py restore <mapping_file> <masked_file>")
                sys.exit(1)
            mapping_file, masked_file = sys.argv[2], sys.argv[3]
            cli.restore_dataset(mapping_file, masked_file)
            
        else:
            print(f"Unknown command: {command}")
            print("Available commands: mask, restore")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
