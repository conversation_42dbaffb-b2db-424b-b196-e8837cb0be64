#!/usr/bin/env python3
"""
Test script for Mathematical Expression Translation Pipeline
==========================================================

This script tests the core functionality of the pipeline without the GUI.
"""

import pandas as pd
import re
import os
import tempfile

def test_masking():
    """Test the masking functionality"""
    print("🎭 Testing masking functionality...")
    
    # Sample data
    test_data = {
        'Question': [
            'Find the derivative of \\frac{x^2 + 1}{x - 1}',
            'Calculate ∫x² dx from 0 to 5',
            'What is the value of x² + 2x + 1?'
        ],
        'Solution': [
            'Using quotient rule: f\'(x) = \\frac{(x-1)(2x) - (x^2+1)(1)}{(x-1)^2}',
            '= [x³/3]₀⁵ = 125/3 - 0 = 125/3',
            'This is a perfect square: (x + 1)²'
        ]
    }
    
    df = pd.DataFrame(test_data)
    
    # Simple masking function
    def mask_text(text):
        if not isinstance(text, str):
            return text, {}
            
        mapping = {}
        masked_text = text
        token_counter = 1
        
        patterns = [
            r'\\frac\{[^}]*\}\{[^}]*\}',
            r'\\[a-zA-Z]+\{[^}]*\}',
            r'[a-zA-Z0-9]+[²³⁴⁵⁶⁷⁸⁹⁰¹]+',
            r'∫[^∫\n]*?(?:dx|dy|dz)',
            r'[²³⁴⁵⁶⁷⁸⁹⁰¹₀₁₂₃₄₅₆₇₈₉]'
        ]
        
        for pattern in patterns:
            matches = []
            for match in re.finditer(pattern, masked_text):
                match_text = match.group()
                if not (match_text.startswith('[') and match_text.endswith(']')):
                    matches.append((match.start(), match.end(), match_text))
            
            for start, end, match_text in reversed(matches):
                token = f'[MATH_{token_counter:03d}]'
                mapping[token] = match_text
                masked_text = masked_text[:start] + token + masked_text[end:]
                token_counter += 1
                
        return masked_text, mapping
    
    # Test masking
    all_mappings = {}
    for column in ['Question', 'Solution']:
        masked_data = []
        for text in df[column]:
            masked_text, local_mapping = mask_text(text)
            masked_data.append(masked_text)
            all_mappings.update(local_mapping)
        df[f'Masked_{column}'] = masked_data
    
    print(f"✅ Masking completed. Found {len(all_mappings)} mathematical expressions.")
    
    # Show examples
    print("\n📝 Examples:")
    for i in range(len(df)):
        print(f"\nRow {i+1}:")
        print(f"  Original: {df.iloc[i]['Question']}")
        print(f"  Masked:   {df.iloc[i]['Masked_Question']}")
    
    return df, all_mappings

def test_restoration(df, mappings):
    """Test the restoration functionality"""
    print("\n🔁 Testing restoration functionality...")
    
    def restore_text(text, mapping):
        if not isinstance(text, str):
            return text
            
        restored_text = text
        sorted_tokens = sorted(mapping.keys(), key=len, reverse=True)
        
        for token in sorted_tokens:
            if token in restored_text:
                restored_text = restored_text.replace(token, mapping[token])
                
        return restored_text
    
    # Test restoration
    for column in ['Masked_Question', 'Masked_Solution']:
        if column in df.columns:
            restored_data = []
            for text in df[column]:
                restored_text = restore_text(text, mappings)
                restored_data.append(restored_text)
            df[f'Restored_{column.replace("Masked_", "")}'] = restored_data
    
    print(f"✅ Restoration completed.")
    
    # Show examples
    print("\n📝 Examples:")
    for i in range(len(df)):
        print(f"\nRow {i+1}:")
        if 'Restored_Question' in df.columns:
            print(f"  Original:  {df.iloc[i]['Question']}")
            print(f"  Masked:    {df.iloc[i]['Masked_Question']}")
            print(f"  Restored:  {df.iloc[i]['Restored_Question']}")
            
            # Check if restoration was perfect
            if df.iloc[i]['Question'] == df.iloc[i]['Restored_Question']:
                print(f"  Status:    ✅ Perfect restoration")
            else:
                print(f"  Status:    ⚠️ Partial restoration")
    
    return df

def test_file_operations():
    """Test file I/O operations"""
    print("\n💾 Testing file operations...")
    
    # Create test data
    test_data = {
        'Question ID': [1, 2, 3],
        'Question': [
            'Find \\frac{a}{b} when a = 4',
            'Calculate x² + y²',
            'What is ∫x dx?'
        ],
        'Answer_key': ['2', '25', 'x²/2 + C'],
        'Solution': [
            'Simply divide: \\frac{4}{b}',
            'Use Pythagorean theorem',
            'Basic integration: ∫x dx = x²/2 + C'
        ]
    }
    
    df = pd.DataFrame(test_data)
    
    # Test CSV operations
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
        csv_file = f.name
        
    try:
        # Save as CSV
        df.to_csv(csv_file, index=False)
        print(f"✅ CSV save successful")
        
        # Load from CSV
        df_loaded = pd.read_csv(csv_file)
        print(f"✅ CSV load successful")
        
        # Verify data integrity
        if len(df) == len(df_loaded) and list(df.columns) == list(df_loaded.columns):
            print(f"✅ Data integrity verified")
        else:
            print(f"❌ Data integrity check failed")
            
    finally:
        # Cleanup
        if os.path.exists(csv_file):
            os.unlink(csv_file)
    
    return True

def test_error_handling():
    """Test error handling and edge cases"""
    print("\n🛡️ Testing error handling...")

    # Test with None values
    def mask_text_safe(text):
        if not isinstance(text, str) or pd.isna(text):
            return text, {}
        return text, {}

    # Test edge cases
    test_cases = [
        None,
        "",
        "   ",
        "Simple text with no math",
        "Text with [MATH_001] token",
        "\\frac{a}{b} + x²"
    ]

    for i, case in enumerate(test_cases):
        try:
            result, mapping = mask_text_safe(case)
            print(f"   ✅ Edge case {i+1}: Handled correctly")
        except Exception as e:
            print(f"   ❌ Edge case {i+1}: Failed - {e}")
            return False

    print("✅ Error handling tests passed")
    return True

def test_dependency_checking():
    """Test dependency availability"""
    print("\n📦 Testing dependencies...")

    required_deps = ['pandas', 'requests', 'tkinter', 'os', 'json', 're']
    optional_deps = ['googletrans', 'openpyxl']

    missing_required = []
    missing_optional = []

    for dep in required_deps:
        try:
            __import__(dep)
            print(f"   ✅ {dep}: Available")
        except ImportError:
            print(f"   ❌ {dep}: Missing (REQUIRED)")
            missing_required.append(dep)

    for dep in optional_deps:
        try:
            __import__(dep)
            print(f"   ✅ {dep}: Available")
        except ImportError:
            print(f"   ⚠️ {dep}: Missing (optional)")
            missing_optional.append(dep)

    if missing_required:
        print(f"❌ Missing required dependencies: {missing_required}")
        return False

    if missing_optional:
        print(f"ℹ️ Missing optional dependencies: {missing_optional}")
        print("   Install with: pip install " + " ".join(missing_optional))

    print("✅ Dependency check completed")
    return True

def main():
    """Run all tests"""
    print("🧪 Mathematical Expression Translation Pipeline - Enhanced Test Suite")
    print("=" * 80)

    all_tests_passed = True

    try:
        # Test 1: Dependencies
        if not test_dependency_checking():
            all_tests_passed = False

        # Test 2: Error handling
        if not test_error_handling():
            all_tests_passed = False

        # Test 3: Masking
        df, mappings = test_masking()

        # Test 4: Restoration
        df = test_restoration(df, mappings)

        # Test 5: File operations
        if not test_file_operations():
            all_tests_passed = False

        print("\n" + "=" * 80)

        if all_tests_passed:
            print("🎉 ALL TESTS PASSED!")
            print("\n📊 Test Summary:")
            print(f"   • Mathematical expressions detected: {len(mappings)}")
            print(f"   • Rows processed: {len(df)}")
            print(f"   • File operations: ✅ Working")
            print(f"   • Masking functionality: ✅ Working")
            print(f"   • Restoration functionality: ✅ Working")
            print(f"   • Error handling: ✅ Working")
            print(f"   • Dependencies: ✅ Checked")

            print("\n🚀 The pipeline is ready to use!")
            print("   Run: python launch_pipeline.py")
        else:
            print("⚠️ SOME TESTS FAILED!")
            print("   Please check the error messages above.")
            print("   The pipeline may still work but with limited functionality.")

    except Exception as e:
        print(f"\n❌ Test suite failed: {str(e)}")
        return False

    return all_tests_passed

if __name__ == "__main__":
    main()
