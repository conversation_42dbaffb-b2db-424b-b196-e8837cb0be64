import pandas as pd
import re

# Load the original data
df = pd.read_excel("GPT TEST.xlsx")

# Global mapping and counters
mapping = {}
token_counters = {"M": 1, "A": 1, "S": 1}

def mask_text(text, prefix):
    """Mask LaTeX/math expressions in text with tokens"""
    print(f"\n=== MASKING WITH PREFIX {prefix} ===")
    print(f"Input type: {type(text)}")
    print(f"Input is NaN: {pd.isna(text)}")
    
    if not isinstance(text, str):
        print(f"Not a string, returning as-is: {text}")
        return text
    
    if pd.isna(text):
        print("Input is NaN, returning as-is")
        return text

    print(f"Input text (first 100 chars): {repr(text[:100])}")
    
    # Store original text for reference
    original_text = text
    masked_text = text

    # Define patterns in order of priority (most specific first)
    patterns = [
        # Complete integral expressions (highest priority)
        r"∫[^∫\n]*?(?:dx|dy|dz|dt|dθ|du|dv|dw)",

        # LaTeX commands with their complete arguments
        r"\\int\\frac\{[^}]*\}\{[^}]*\}d\\theta",
        r"\\frac\{[^}]*\}\{[^}]*\}",
        r"\\sqrt\{[^}]*\}",
        r"\\[a-zA-Z]+\{[^}]*\}",
        r"\\[a-zA-Z]+",

        # Mathematical expressions with superscripts/subscripts
        r"[a-zA-Z0-9]+\^[a-zA-Z0-9⁄()]+",
        r"[a-zA-Z0-9]+_[a-zA-Z0-9⁄()]+",
        r"[a-zA-Z0-9]+[²³⁴⁵⁶⁷⁸⁹⁰¹]+",
        r"[a-zA-Z0-9]+[₀₁₂₃₄₅₆₇₈₉]+",

        # Fractions
        r"[0-9]+⁄[0-9]+",

        # Square roots with Unicode
        r"√[a-zA-Z0-9]+",

        # Mathematical symbols and special characters
        r"[ℝ∞±∉]",
    ]

    # Apply patterns one by one, being careful not to re-process already masked content
    for pattern in patterns:
        # Find all matches in the current state of masked_text
        matches = []
        for match in re.finditer(pattern, masked_text):
            match_text = match.group()
            # Skip if this is already a token
            if not (match_text.startswith('<') and match_text.endswith('>')):
                matches.append((match.start(), match.end(), match_text))

        # Replace matches from right to left to preserve positions
        for start, end, match_text in reversed(matches):
            token = f"<{prefix}{token_counters[prefix]}>"
            mapping[token] = match_text
            masked_text = masked_text[:start] + token + masked_text[end:]
            token_counters[prefix] += 1

    print(f"Output text (first 100 chars): {repr(masked_text[:100])}")
    return masked_text

# Test on rows 12-16 specifically
print("=== TESTING SOLUTION MASKING ON ROWS 12-16 ===")
for i in range(11, min(16, len(df))):
    row_num = i + 1
    print(f"\n--- ROW {row_num} ---")
    solution_value = df.iloc[i]['Solution']
    masked_solution = mask_text(solution_value, "S")
    print(f"Final result: {repr(masked_solution)}")
