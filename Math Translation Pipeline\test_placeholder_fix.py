#!/usr/bin/env python3
"""
Test Placeholder Restoration Fix
===============================

Test the specific issue where placeholders like 'MathToken0000PlaceHolder' 
are not being restored back to mathematical expressions.
"""

import re

def test_placeholder_restoration():
    """Test the complete placeholder restoration chain"""
    print("🧪 Testing Placeholder Restoration Chain...")
    
    # Original text with mathematical expressions
    original_text = r"\text{Find the limit of each of the sequences } (a_n) \text{ in the following cases:} a_n = \frac{1}{2^n}"
    
    # Step 1: Simulate masking
    print("\n📝 Step 1: Masking")
    print(f"Original: {original_text}")
    
    # Simple masking simulation
    masked_text = original_text
    mapping = {}
    token_counter = 0
    
    patterns = [
        (r'\\text\{[^}]*\}', 'LaTeX text'),
        (r'\\frac\{[^}]*\}\{[^}]*\}', 'LaTeX fraction'),
        (r'[a-zA-Z_]+', 'Variable'),
    ]
    
    for pattern, desc in patterns:
        matches = []
        for match in re.finditer(pattern, masked_text):
            match_text = match.group()
            if not (match_text.startswith('[') and match_text.endswith(']')):
                matches.append((match.start(), match.end(), match_text))
        
        for start, end, match_text in reversed(matches):
            token = f'[MATH_{token_counter:03d}]'
            mapping[token] = {'original': match_text, 'description': desc}
            masked_text = masked_text[:start] + token + masked_text[end:]
            token_counter += 1
    
    print(f"Masked: {masked_text}")
    print(f"Mapping: {mapping}")
    
    # Step 2: Simulate token protection
    print("\n🛡️ Step 2: Token Protection")
    
    def protect_tokens(text):
        tokens = re.findall(r'\[MATH_\d+\]', text)
        protected_text = text
        token_map = {}
        
        for i, token in enumerate(tokens):
            placeholder = f"MATHTOKEN{i:04d}PLACEHOLDER"
            token_map[placeholder] = token
            protected_text = protected_text.replace(token, placeholder)
        
        return protected_text, token_map
    
    protected_text, token_map = protect_tokens(masked_text)
    print(f"Protected: {protected_text}")
    print(f"Token Map: {token_map}")
    
    # Step 3: Simulate translation modifications
    print("\n🌐 Step 3: Translation (with modifications)")
    
    # Simulate what translation services might do to placeholders
    translated_text = protected_text
    translated_text = translated_text.replace('MATHTOKEN', 'MathToken')
    translated_text = translated_text.replace('PLACEHOLDER', 'PlaceHolder')
    # Add some text translation
    translated_text = "Temukan batas dari setiap urutan " + translated_text + " dalam kasus berikut:"
    
    print(f"Translated: {translated_text}")
    
    # Step 4: Enhanced placeholder restoration
    print("\n🔄 Step 4: Enhanced Placeholder Restoration")
    
    def enhanced_restore_placeholders(text, token_map):
        if not isinstance(text, str) or not token_map:
            return text

        restored_text = text
        
        # Create extended mapping with variations
        extended_token_map = {}
        for placeholder, original_token in token_map.items():
            extended_token_map[placeholder] = original_token
            
            variations = [
                placeholder.lower(),
                placeholder.upper(),
                placeholder.title(),
                placeholder.replace('MATHTOKEN', 'MathToken'),
                placeholder.replace('PLACEHOLDER', 'PlaceHolder'),
                placeholder.replace('MATHTOKEN', 'MathToken').replace('PLACEHOLDER', 'PlaceHolder'),
            ]
            
            for variation in variations:
                if variation != placeholder:
                    extended_token_map[variation] = original_token
        
        # Sort by length (longest first)
        sorted_placeholders = sorted(extended_token_map.keys(), key=len, reverse=True)
        
        for placeholder in sorted_placeholders:
            if placeholder in restored_text:
                original_token = extended_token_map[placeholder]
                restored_text = restored_text.replace(placeholder, original_token)
                print(f"   Restored {placeholder} → {original_token}")

        return restored_text
    
    token_restored_text = enhanced_restore_placeholders(translated_text, token_map)
    print(f"Token Restored: {token_restored_text}")
    
    # Step 5: Mathematical expression restoration
    print("\n🔁 Step 5: Mathematical Expression Restoration")
    
    def restore_math_expressions(text, mapping):
        if not isinstance(text, str):
            return text
            
        restored_text = text
        
        # Sort tokens by length (longest first)
        sorted_tokens = sorted(mapping.keys(), key=len, reverse=True)
        
        for token in sorted_tokens:
            if token in restored_text:
                original_expression = mapping[token]['original']
                restored_text = restored_text.replace(token, original_expression)
                print(f"   Restored {token} → {original_expression}")
        
        return restored_text
    
    final_text = restore_math_expressions(token_restored_text, mapping)
    print(f"Final: {final_text}")
    
    # Step 6: Validation
    print("\n✅ Step 6: Validation")
    
    # Check if any placeholders remain
    remaining_placeholders = re.findall(r'(?:MATHTOKEN|MathToken|mathtoken)\d+(?:PLACEHOLDER|PlaceHolder|placeholder)', final_text)
    remaining_tokens = re.findall(r'\[MATH_\d+\]', final_text)
    
    if remaining_placeholders:
        print(f"❌ Remaining placeholders: {remaining_placeholders}")
        return False
    elif remaining_tokens:
        print(f"⚠️ Remaining tokens: {remaining_tokens}")
        return False
    else:
        print("✅ All placeholders and tokens successfully restored!")
        
        # Check if original mathematical expressions are present
        original_expressions = [info['original'] for info in mapping.values()]
        expressions_found = sum(1 for expr in original_expressions if expr in final_text)
        
        print(f"✅ Mathematical expressions restored: {expressions_found}/{len(original_expressions)}")
        
        if expressions_found == len(original_expressions):
            print("🎉 COMPLETE SUCCESS: All mathematical expressions properly restored!")
            return True
        else:
            print("⚠️ PARTIAL SUCCESS: Some expressions may be missing")
            return expressions_found > 0

def main():
    """Run the placeholder restoration test"""
    print("🧪 Placeholder Restoration Fix Test")
    print("=" * 60)
    
    success = test_placeholder_restoration()
    
    print("\n" + "=" * 60)
    
    if success:
        print("🎉 PLACEHOLDER RESTORATION TEST PASSED!")
        print("✅ The fix should resolve the MathToken0000PlaceHolder issue")
    else:
        print("❌ PLACEHOLDER RESTORATION TEST FAILED!")
        print("⚠️ Additional fixes may be needed")
    
    return success

if __name__ == "__main__":
    main()
