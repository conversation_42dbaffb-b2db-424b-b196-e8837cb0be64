import pandas as pd

# Load the CSV files
df_masked = pd.read_csv("masked_GPT_TEST_FINAL.csv")
df_mapping = pd.read_csv("mapping_table_FINAL.csv")

print("=== CSV VERIFICATION ===")
print("Rows 12-16 from CSV Masked Dataset:")
for i in range(11, min(16, len(df_masked))):
    row_num = i + 1
    solution = df_masked.iloc[i]['Masked Solution']
    print(f"Row {row_num}:")
    print(f"  Type: {type(solution)}")
    print(f"  Is NaN: {pd.isna(solution)}")
    if not pd.isna(solution):
        print(f"  Content (first 100 chars): {repr(solution[:100])}")
    else:
        print(f"  Content: NaN")
    print()

print(f"Mapping table contains {len(df_mapping)} entries")

# Summary
total_rows = len(df_masked)
empty_solutions = df_masked['Masked Solution'].isna().sum()
non_empty_solutions = total_rows - empty_solutions

print(f"\n=== FINAL SUMMARY ===")
print(f"Total rows: {total_rows}")
print(f"Empty masked solutions: {empty_solutions}")
print(f"Non-empty masked solutions: {non_empty_solutions}")
print(f"Success rate: {(non_empty_solutions/total_rows)*100:.1f}%")

if empty_solutions == 0:
    print("\n🎉 PERFECT SUCCESS! 🎉")
    print("✅ All Solution cells have been properly masked!")
    print("✅ No more empty cells in the Masked Solution column!")
    print("✅ Mathematical expressions are now tokenized as short identifiers!")
    print(f"✅ {len(df_mapping)} unique mathematical expressions have been mapped!")
else:
    print(f"\n⚠️  WARNING: {empty_solutions} Solution cells are still empty")

print("\n📁 FILES CREATED:")
print("  • masked_GPT_TEST_FINAL.csv - Your masked dataset")
print("  • mapping_table_FINAL.csv - Token-to-expression mappings")
print("\n💡 USAGE:")
print("  1. Use the masked dataset for your processing")
print("  2. Use the mapping table to convert tokens back to original expressions when needed")
