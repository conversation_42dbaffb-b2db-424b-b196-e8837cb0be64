import pandas as pd
import re

# Load the Excel file
df = pd.read_excel("GPT TEST.xlsx", sheet_name="Sheet1")

# Let's specifically examine rows 12-16 and see what happens during masking
print("=== DEBUGGING MASKING FOR ROWS 12-16 ===\n")

def debug_mask_text(text, prefix, row_num):
    """Debug version of mask_text function"""
    if not isinstance(text, str):
        print(f"Row {row_num} - {prefix}: Not a string, value is {type(text)}: {repr(text)}")
        return text

    print(f"Row {row_num} - {prefix}: Original text:")
    print(f"  '{text}'")
    print(f"  Length: {len(text)}")

    # Check if text is empty or just whitespace
    if not text.strip():
        print(f"  -> Text is empty or whitespace only")
        return text

    # Store original text for reference
    original_text = text
    masked_text = text
    token_counter = 1

    # Define patterns in order of priority (most specific first)
    patterns = [
        # Complete integral expressions (highest priority)
        r"∫[^∫\n]*?(?:dx|dy|dz|dt|dθ|du|dv|dw)",

        # LaTeX commands with their complete arguments
        r"\\int\\frac\{[^}]*\}\{[^}]*\}d\\theta",
        r"\\frac\{[^}]*\}\{[^}]*\}",
        r"\\sqrt\{[^}]*\}",
        r"\\[a-zA-Z]+\{[^}]*\}",
        r"\\[a-zA-Z]+",

        # Mathematical expressions with superscripts/subscripts
        r"[a-zA-Z0-9]+\^[a-zA-Z0-9⁄()]+",
        r"[a-zA-Z0-9]+_[a-zA-Z0-9⁄()]+",
        r"[a-zA-Z0-9]+[²³⁴⁵⁶⁷⁸⁹⁰¹]+",
        r"[a-zA-Z0-9]+[₀₁₂₃₄₅₆₇₈₉]+",

        # Fractions
        r"[0-9]+⁄[0-9]+",

        # Square roots with Unicode
        r"√[a-zA-Z0-9]+",

        # Mathematical symbols and special characters
        r"[ℝ∞±∉]",
    ]

    # Apply patterns one by one, being careful not to re-process already masked content
    for i, pattern in enumerate(patterns):
        # Find all matches in the current state of masked_text
        matches = []
        for match in re.finditer(pattern, masked_text):
            match_text = match.group()
            # Skip if this is already a token
            if not (match_text.startswith('<') and match_text.endswith('>')):
                matches.append((match.start(), match.end(), match_text))

        if matches:
            print(f"  Pattern {i+1} matched: {[m[2] for m in matches]}")

        # Replace matches from right to left to preserve positions
        for start, end, match_text in reversed(matches):
            token = f"<{prefix}{token_counter}>"
            masked_text = masked_text[:start] + token + masked_text[end:]
            token_counter += 1

    print(f"  Total tokens created: {token_counter - 1}")
    print(f"  Final masked text: '{masked_text}'")
    print()

    return masked_text

# Test on rows 12-16
for i in range(11, min(16, len(df))):
    row_num = i + 1
    print(f"=== ROW {row_num} ===")
    
    question = df.iloc[i]['Question']
    answer_key = df.iloc[i]['Answer_key'] 
    solution = df.iloc[i]['Solution']
    
    debug_mask_text(question, "M", row_num)
    debug_mask_text(answer_key, "A", row_num)
    debug_mask_text(solution, "S", row_num)
    print("-" * 80)
