#!/usr/bin/env python3
"""
Simple Test for Placeholder Fix
===============================

Test the specific case from the user's example.
"""

import re

def test_user_case():
    """Test the exact case from the user"""
    print("🧪 Testing User's Specific Case...")
    
    # User's original question
    original = r"\text{Find the limit of each of the sequences } (a_n) \text{ in the following cases:} a_n = \frac{1}{2^n}"
    print(f"Original: {original}")
    
    # Step 1: Better masking (avoid over-masking)
    def smart_mask(text):
        mapping = {}
        masked_text = text
        counter = 1
        
        # Only mask meaningful mathematical expressions
        patterns = [
            (r'\\text\{[^}]+\}', 'LaTeX text'),
            (r'\\frac\{[^}]+\}\{[^}]+\}', 'LaTeX fraction'),
            (r'[a-zA-Z]+_[a-zA-Z0-9]+', 'Variable with subscript'),
        ]
        
        for pattern, desc in patterns:
            matches = list(re.finditer(pattern, masked_text))
            for match in reversed(matches):  # Right to left
                if not ('[MATH_' in match.group() and ']' in match.group()):
                    token = f'[MATH_{counter:03d}]'
                    mapping[token] = {'original': match.group(), 'description': desc}
                    masked_text = masked_text[:match.start()] + token + masked_text[match.end():]
                    counter += 1
        
        return masked_text, mapping
    
    masked, mapping = smart_mask(original)
    print(f"Masked: {masked}")
    print(f"Mapping: {mapping}")
    
    # Step 2: Token protection
    def protect_tokens(text):
        tokens = re.findall(r'\[MATH_\d+\]', text)
        protected = text
        token_map = {}
        
        for i, token in enumerate(tokens):
            placeholder = f"MATHTOKEN{i:04d}PLACEHOLDER"
            token_map[placeholder] = token
            protected = protected.replace(token, placeholder)
        
        return protected, token_map
    
    protected, token_map = protect_tokens(masked)
    print(f"Protected: {protected}")
    
    # Step 3: Simulate translation with modifications
    translated = protected.replace('MATHTOKEN', 'MathToken').replace('PLACEHOLDER', 'PlaceHolder')
    print(f"Translated: {translated}")
    
    # Step 4: Restore placeholders
    def restore_placeholders(text, token_map):
        restored = text
        
        # Handle variations
        for original_placeholder, token in token_map.items():
            variations = [
                original_placeholder,
                original_placeholder.replace('MATHTOKEN', 'MathToken'),
                original_placeholder.replace('PLACEHOLDER', 'PlaceHolder'),
                original_placeholder.replace('MATHTOKEN', 'MathToken').replace('PLACEHOLDER', 'PlaceHolder'),
            ]
            
            for variation in variations:
                if variation in restored:
                    restored = restored.replace(variation, token)
                    print(f"   Restored placeholder: {variation} → {token}")
        
        return restored
    
    token_restored = restore_placeholders(translated, token_map)
    print(f"Token Restored: {token_restored}")
    
    # Step 5: Restore mathematical expressions
    def restore_math(text, mapping):
        restored = text
        
        for token, info in mapping.items():
            if token in restored:
                restored = restored.replace(token, info['original'])
                print(f"   Restored math: {token} → {info['original']}")
        
        return restored
    
    final = restore_math(token_restored, mapping)
    print(f"Final: {final}")
    
    # Check success
    placeholders_remaining = bool(re.search(r'(?:MATHTOKEN|MathToken)\d+(?:PLACEHOLDER|PlaceHolder)', final))
    tokens_remaining = bool(re.search(r'\[MATH_\d+\]', final))
    
    if not placeholders_remaining and not tokens_remaining:
        print("✅ SUCCESS: All placeholders and tokens restored!")
        return True
    else:
        print(f"❌ FAILED: Placeholders remaining: {placeholders_remaining}, Tokens remaining: {tokens_remaining}")
        return False

def main():
    print("🧪 Simple Placeholder Fix Test")
    print("=" * 50)
    
    success = test_user_case()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 TEST PASSED! The placeholder issue should be fixed.")
    else:
        print("❌ TEST FAILED! More work needed.")

if __name__ == "__main__":
    main()
