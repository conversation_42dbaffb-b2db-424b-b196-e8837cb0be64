import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import re
import os
from pathlib import Path

class MathMaskingApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Mathematical Expression Masking Tool")
        self.root.geometry("800x600")
        
        # Variables
        self.input_file = tk.StringVar()
        self.output_prefix = tk.StringVar(value="masked_dataset")
        self.mapping_file = tk.StringVar()
        self.restore_file = tk.StringVar()
        
        # Data storage
        self.mapping = {}
        self.token_counters = {"M": 1, "A": 1, "S": 1}
        
        self.create_widgets()
        
    def create_widgets(self):
        # Create notebook for tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Tab 1: Masking
        self.create_masking_tab(notebook)
        
        # Tab 2: Restoration
        self.create_restoration_tab(notebook)
        
        # Tab 3: About
        self.create_about_tab(notebook)
        
    def create_masking_tab(self, notebook):
        mask_frame = ttk.Frame(notebook)
        notebook.add(mask_frame, text="Mask Mathematical Expressions")
        
        # Title
        title_label = ttk.Label(mask_frame, text="Mathematical Expression Masking", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # Input file selection
        input_frame = ttk.LabelFrame(mask_frame, text="Input File", padding=10)
        input_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(input_frame, text="Select Excel file to process:").pack(anchor=tk.W)
        
        file_frame = ttk.Frame(input_frame)
        file_frame.pack(fill=tk.X, pady=5)
        
        ttk.Entry(file_frame, textvariable=self.input_file, width=60).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(file_frame, text="Browse", command=self.browse_input_file).pack(side=tk.RIGHT, padx=(5,0))
        
        # Output settings
        output_frame = ttk.LabelFrame(mask_frame, text="Output Settings", padding=10)
        output_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(output_frame, text="Output file prefix:").pack(anchor=tk.W)
        ttk.Entry(output_frame, textvariable=self.output_prefix, width=30).pack(anchor=tk.W, pady=2)
        
        # Process button
        process_frame = ttk.Frame(mask_frame)
        process_frame.pack(pady=20)
        
        ttk.Button(process_frame, text="Process & Mask Expressions", 
                  command=self.process_masking, style="Accent.TButton").pack()
        
        # Progress and results
        self.mask_progress = ttk.Progressbar(mask_frame, mode='indeterminate')
        self.mask_progress.pack(fill=tk.X, padx=10, pady=5)
        
        self.mask_result_text = tk.Text(mask_frame, height=15, wrap=tk.WORD)
        scrollbar1 = ttk.Scrollbar(mask_frame, orient=tk.VERTICAL, command=self.mask_result_text.yview)
        self.mask_result_text.configure(yscrollcommand=scrollbar1.set)
        
        text_frame1 = ttk.Frame(mask_frame)
        text_frame1.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.mask_result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, in_=text_frame1)
        scrollbar1.pack(side=tk.RIGHT, fill=tk.Y, in_=text_frame1)
        
    def create_restoration_tab(self, notebook):
        restore_frame = ttk.Frame(notebook)
        notebook.add(restore_frame, text="Restore Original Expressions")
        
        # Title
        title_label = ttk.Label(restore_frame, text="Mathematical Expression Restoration", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # Mapping file selection
        mapping_frame = ttk.LabelFrame(restore_frame, text="Mapping File", padding=10)
        mapping_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(mapping_frame, text="Select mapping table file (CSV):").pack(anchor=tk.W)
        
        map_file_frame = ttk.Frame(mapping_frame)
        map_file_frame.pack(fill=tk.X, pady=5)
        
        ttk.Entry(map_file_frame, textvariable=self.mapping_file, width=60).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(map_file_frame, text="Browse", command=self.browse_mapping_file).pack(side=tk.RIGHT, padx=(5,0))
        
        # Masked file selection
        masked_frame = ttk.LabelFrame(restore_frame, text="Masked Dataset", padding=10)
        masked_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(masked_frame, text="Select masked dataset file (CSV):").pack(anchor=tk.W)
        
        restore_file_frame = ttk.Frame(masked_frame)
        restore_file_frame.pack(fill=tk.X, pady=5)
        
        ttk.Entry(restore_file_frame, textvariable=self.restore_file, width=60).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(restore_file_frame, text="Browse", command=self.browse_restore_file).pack(side=tk.RIGHT, padx=(5,0))
        
        # Restore button
        restore_button_frame = ttk.Frame(restore_frame)
        restore_button_frame.pack(pady=20)
        
        ttk.Button(restore_button_frame, text="Restore Original Expressions", 
                  command=self.process_restoration, style="Accent.TButton").pack()
        
        # Progress and results
        self.restore_progress = ttk.Progressbar(restore_frame, mode='indeterminate')
        self.restore_progress.pack(fill=tk.X, padx=10, pady=5)
        
        self.restore_result_text = tk.Text(restore_frame, height=15, wrap=tk.WORD)
        scrollbar2 = ttk.Scrollbar(restore_frame, orient=tk.VERTICAL, command=self.restore_result_text.yview)
        self.restore_result_text.configure(yscrollcommand=scrollbar2.set)
        
        text_frame2 = ttk.Frame(restore_frame)
        text_frame2.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.restore_result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, in_=text_frame2)
        scrollbar2.pack(side=tk.RIGHT, fill=tk.Y, in_=text_frame2)
        
    def create_about_tab(self, notebook):
        about_frame = ttk.Frame(notebook)
        notebook.add(about_frame, text="About")
        
        about_text = """
Mathematical Expression Masking Tool
====================================

This tool helps you process datasets containing mathematical expressions by:

1. MASKING: Converting complex mathematical expressions into short tokens
   • LaTeX expressions (\\frac{a}{b}, \\sqrt{x}, etc.)
   • Unicode mathematical symbols (∫, √, ², ³, etc.)
   • Mathematical notation (subscripts, superscripts)
   
2. RESTORATION: Converting tokens back to original expressions
   • Uses mapping table to restore original content
   • Preserves all mathematical formatting
   
Features:
• Handles Excel and CSV files
• Processes Question, Answer Key, and Solution columns
• Creates detailed mapping tables
• User-friendly interface
• Progress tracking

Usage:
1. Use the "Mask" tab to convert mathematical expressions to tokens
2. Process your data with the masked dataset
3. Use the "Restore" tab to convert tokens back to original expressions

File Formats Supported:
• Input: Excel (.xlsx), CSV (.csv)
• Output: CSV (recommended for complex mathematical content)

Created for processing educational datasets with mathematical content.
        """
        
        about_label = tk.Text(about_frame, wrap=tk.WORD, padx=20, pady=20)
        about_label.insert(tk.END, about_text)
        about_label.config(state=tk.DISABLED)
        about_label.pack(fill=tk.BOTH, expand=True)
        
    def browse_input_file(self):
        filename = filedialog.askopenfilename(
            title="Select input file",
            filetypes=[("Excel files", "*.xlsx"), ("CSV files", "*.csv"), ("All files", "*.*")]
        )
        if filename:
            self.input_file.set(filename)
            
    def browse_mapping_file(self):
        filename = filedialog.askopenfilename(
            title="Select mapping table file",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )
        if filename:
            self.mapping_file.set(filename)
            
    def browse_restore_file(self):
        filename = filedialog.askopenfilename(
            title="Select masked dataset file",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )
        if filename:
            self.restore_file.set(filename)

    def clean_for_excel(self, text):
        """Clean text to be Excel-compatible by removing problematic characters"""
        if not isinstance(text, str):
            return text

        # Replace problematic Unicode characters with ASCII equivalents
        replacements = {
            '²': '^2', '³': '^3', '⁴': '^4', '⁵': '^5', '⁶': '^6', '⁷': '^7', '⁸': '^8', '⁹': '^9', '⁰': '^0', '¹': '^1',
            '₀': '_0', '₁': '_1', '₂': '_2', '₃': '_3', '₄': '_4', '₅': '_5', '₆': '_6', '₇': '_7', '₈': '_8', '₉': '_9',
            '⁄': '/', '√': 'sqrt', '∫': 'integral', '∞': 'infinity', '±': '+/-', '∉': 'not_in', 'ℝ': 'R', 'θ': 'theta',
            '½': '1/2', '⅓': '1/3', '¼': '1/4', '¾': '3/4', '→': '->'
        }

        cleaned_text = text
        for unicode_char, ascii_replacement in replacements.items():
            cleaned_text = cleaned_text.replace(unicode_char, ascii_replacement)

        return cleaned_text

    def mask_text(self, text, prefix):
        """Mask LaTeX/math expressions in text with tokens"""
        if not isinstance(text, str):
            return text

        original_text = text
        masked_text = text

        # Define patterns in order of priority (most specific first)
        patterns = [
            r"∫[^∫\n]*?(?:dx|dy|dz|dt|dθ|du|dv|dw)",  # Complete integral expressions
            r"\\int\\frac\{[^}]*\}\{[^}]*\}d\\theta",  # LaTeX integral fractions
            r"\\frac\{[^}]*\}\{[^}]*\}",  # LaTeX fractions
            r"\\sqrt\{[^}]*\}",  # LaTeX square roots
            r"\\[a-zA-Z]+\{[^}]*\}",  # LaTeX commands with arguments
            r"\\[a-zA-Z]+",  # LaTeX commands
            r"[a-zA-Z0-9]+\^[a-zA-Z0-9⁄()]+",  # Superscripts
            r"[a-zA-Z0-9]+_[a-zA-Z0-9⁄()]+",  # Subscripts
            r"[a-zA-Z0-9]+[²³⁴⁵⁶⁷⁸⁹⁰¹]+",  # Unicode superscripts
            r"[a-zA-Z0-9]+[₀₁₂₃₄₅₆₇₈₉]+",  # Unicode subscripts
            r"[0-9]+⁄[0-9]+",  # Unicode fractions
            r"√[a-zA-Z0-9]+",  # Unicode square roots
            r"[ℝ∞±∉]",  # Mathematical symbols
        ]

        # Apply patterns one by one
        for pattern in patterns:
            matches = []
            for match in re.finditer(pattern, masked_text):
                match_text = match.group()
                if not (match_text.startswith('<') and match_text.endswith('>')):
                    matches.append((match.start(), match.end(), match_text))

            # Replace matches from right to left to preserve positions
            for start, end, match_text in reversed(matches):
                token = f"<{prefix}{self.token_counters[prefix]}>"
                self.mapping[token] = match_text
                masked_text = masked_text[:start] + token + masked_text[end:]
                self.token_counters[prefix] += 1

        # Clean the masked text for Excel compatibility
        masked_text = self.clean_for_excel(masked_text)

        return masked_text

    def process_masking(self):
        """Process the input file and create masked dataset"""
        if not self.input_file.get():
            messagebox.showerror("Error", "Please select an input file")
            return

        if not self.output_prefix.get():
            messagebox.showerror("Error", "Please enter an output file prefix")
            return

        try:
            self.mask_progress.start()
            self.mask_result_text.delete(1.0, tk.END)
            self.mask_result_text.insert(tk.END, "Starting processing...\n")
            self.root.update()

            # Reset counters and mapping
            self.mapping = {}
            self.token_counters = {"M": 1, "A": 1, "S": 1}

            # Load input file
            input_path = self.input_file.get()
            self.mask_result_text.insert(tk.END, f"Loading file: {input_path}\n")
            self.root.update()

            if input_path.endswith('.xlsx'):
                df = pd.read_excel(input_path)
            elif input_path.endswith('.csv'):
                df = pd.read_csv(input_path)
            else:
                raise ValueError("Unsupported file format. Please use .xlsx or .csv files.")

            self.mask_result_text.insert(tk.END, f"Loaded {len(df)} rows\n")
            self.root.update()

            # Process each column
            df_result = df.copy()

            # Process Question column
            if 'Question' in df.columns:
                self.mask_result_text.insert(tk.END, "Processing Question column...\n")
                self.root.update()
                masked_questions = []
                for question in df["Question"]:
                    if pd.isna(question):
                        masked_questions.append(question)
                    else:
                        masked = self.mask_text(question, "M")
                        masked_questions.append(masked)
                df_result["Masked Question"] = masked_questions

            # Process Answer Key column
            if 'Answer_key' in df.columns:
                self.mask_result_text.insert(tk.END, "Processing Answer Key column...\n")
                self.root.update()
                masked_answers = []
                for answer in df["Answer_key"]:
                    if pd.isna(answer):
                        masked_answers.append(answer)
                    else:
                        masked = self.mask_text(answer, "A")
                        masked_answers.append(masked)
                df_result["Masked Answer Key"] = masked_answers

            # Process Solution column
            if 'Solution' in df.columns:
                self.mask_result_text.insert(tk.END, "Processing Solution column...\n")
                self.root.update()
                masked_solutions = []
                for solution in df["Solution"]:
                    if pd.isna(solution):
                        masked_solutions.append(solution)
                    else:
                        masked = self.mask_text(solution, "S")
                        masked_solutions.append(masked)
                df_result["Masked Solution"] = masked_solutions

            # Prepare final dataset
            columns_to_keep = ["Question ID", "Subject", "Chapter name", "Topic", "Question Image tag / Url", "Solution Image tag / Url"]
            available_columns = [col for col in columns_to_keep if col in df_result.columns]

            if 'Question' in df.columns:
                available_columns.append("Masked Question")
            if 'Answer_key' in df.columns:
                available_columns.append("Masked Answer Key")
            if 'Solution' in df.columns:
                available_columns.append("Masked Solution")

            df_masked = df_result[available_columns]

            # Create mapping table
            df_mapping = pd.DataFrame(list(self.mapping.items()), columns=["Token", "Original Math Expression"])

            # Save files
            output_prefix = self.output_prefix.get()
            masked_file = f"{output_prefix}_dataset.csv"
            mapping_file = f"{output_prefix}_mapping.csv"

            self.mask_result_text.insert(tk.END, f"Saving masked dataset to: {masked_file}\n")
            self.root.update()
            df_masked.to_csv(masked_file, index=False)

            self.mask_result_text.insert(tk.END, f"Saving mapping table to: {mapping_file}\n")
            self.root.update()
            df_mapping.to_csv(mapping_file, index=False)

            # Results summary
            total_rows = len(df_masked)
            empty_solutions = 0
            if 'Masked Solution' in df_masked.columns:
                empty_solutions = df_masked['Masked Solution'].isna().sum()

            self.mask_result_text.insert(tk.END, f"\n=== PROCESSING COMPLETE ===\n")
            self.mask_result_text.insert(tk.END, f"✅ Total rows processed: {total_rows}\n")
            self.mask_result_text.insert(tk.END, f"✅ Mathematical expressions tokenized: {len(self.mapping)}\n")
            if 'Masked Solution' in df_masked.columns:
                self.mask_result_text.insert(tk.END, f"✅ Empty solution cells: {empty_solutions}\n")
                self.mask_result_text.insert(tk.END, f"✅ Success rate: {((total_rows-empty_solutions)/total_rows)*100:.1f}%\n")

            self.mask_result_text.insert(tk.END, f"\n📁 Files created:\n")
            self.mask_result_text.insert(tk.END, f"  • {masked_file}\n")
            self.mask_result_text.insert(tk.END, f"  • {mapping_file}\n")

            messagebox.showinfo("Success", f"Processing complete!\n\nFiles created:\n• {masked_file}\n• {mapping_file}")

        except Exception as e:
            self.mask_result_text.insert(tk.END, f"\n❌ ERROR: {str(e)}\n")
            messagebox.showerror("Error", f"An error occurred: {str(e)}")
        finally:
            self.mask_progress.stop()

    def restore_text(self, text, token_mapping):
        """Restore tokens back to original mathematical expressions"""
        if not isinstance(text, str):
            return text

        restored_text = text

        # Sort tokens by length (longest first) to avoid partial replacements
        sorted_tokens = sorted(token_mapping.keys(), key=len, reverse=True)

        for token in sorted_tokens:
            if token in restored_text:
                original_expression = token_mapping[token]
                restored_text = restored_text.replace(token, original_expression)

        return restored_text

    def process_restoration(self):
        """Process the masked dataset and restore original expressions"""
        if not self.mapping_file.get():
            messagebox.showerror("Error", "Please select a mapping table file")
            return

        if not self.restore_file.get():
            messagebox.showerror("Error", "Please select a masked dataset file")
            return

        try:
            self.restore_progress.start()
            self.restore_result_text.delete(1.0, tk.END)
            self.restore_result_text.insert(tk.END, "Starting restoration...\n")
            self.root.update()

            # Load mapping table
            mapping_path = self.mapping_file.get()
            self.restore_result_text.insert(tk.END, f"Loading mapping table: {mapping_path}\n")
            self.root.update()

            df_mapping = pd.read_csv(mapping_path)
            token_mapping = dict(zip(df_mapping['Token'], df_mapping['Original Math Expression']))

            self.restore_result_text.insert(tk.END, f"Loaded {len(token_mapping)} token mappings\n")
            self.root.update()

            # Load masked dataset
            dataset_path = self.restore_file.get()
            self.restore_result_text.insert(tk.END, f"Loading masked dataset: {dataset_path}\n")
            self.root.update()

            df_masked = pd.read_csv(dataset_path)
            self.restore_result_text.insert(tk.END, f"Loaded {len(df_masked)} rows\n")
            self.root.update()

            # Create restored dataset
            df_restored = df_masked.copy()

            # Restore each column that contains tokens
            columns_to_restore = []
            if 'Masked Question' in df_masked.columns:
                columns_to_restore.append(('Masked Question', 'Restored Question'))
            if 'Masked Answer Key' in df_masked.columns:
                columns_to_restore.append(('Masked Answer Key', 'Restored Answer Key'))
            if 'Masked Solution' in df_masked.columns:
                columns_to_restore.append(('Masked Solution', 'Restored Solution'))

            for masked_col, restored_col in columns_to_restore:
                self.restore_result_text.insert(tk.END, f"Restoring {masked_col}...\n")
                self.root.update()

                restored_data = []
                for text in df_masked[masked_col]:
                    if pd.isna(text):
                        restored_data.append(text)
                    else:
                        restored = self.restore_text(text, token_mapping)
                        restored_data.append(restored)

                df_restored[restored_col] = restored_data

            # Save restored dataset
            base_name = Path(dataset_path).stem
            restored_file = f"{base_name}_RESTORED.csv"

            self.restore_result_text.insert(tk.END, f"Saving restored dataset to: {restored_file}\n")
            self.root.update()
            df_restored.to_csv(restored_file, index=False)

            # Results summary
            total_rows = len(df_restored)
            tokens_restored = 0

            # Count how many tokens were restored
            for text in df_masked[columns_to_restore[0][0]] if columns_to_restore else []:
                if isinstance(text, str):
                    tokens_restored += len([token for token in token_mapping.keys() if token in text])

            self.restore_result_text.insert(tk.END, f"\n=== RESTORATION COMPLETE ===\n")
            self.restore_result_text.insert(tk.END, f"✅ Total rows processed: {total_rows}\n")
            self.restore_result_text.insert(tk.END, f"✅ Columns restored: {len(columns_to_restore)}\n")
            self.restore_result_text.insert(tk.END, f"✅ Token mappings used: {len(token_mapping)}\n")

            self.restore_result_text.insert(tk.END, f"\n📁 File created:\n")
            self.restore_result_text.insert(tk.END, f"  • {restored_file}\n")

            self.restore_result_text.insert(tk.END, f"\n💡 The restored file contains both masked and restored columns for comparison.\n")

            messagebox.showinfo("Success", f"Restoration complete!\n\nFile created:\n• {restored_file}")

        except Exception as e:
            self.restore_result_text.insert(tk.END, f"\n❌ ERROR: {str(e)}\n")
            messagebox.showerror("Error", f"An error occurred: {str(e)}")
        finally:
            self.restore_progress.stop()

def main():
    root = tk.Tk()

    # Configure style
    style = ttk.Style()
    style.theme_use('clam')

    # Create and run the application
    app = MathMaskingApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
