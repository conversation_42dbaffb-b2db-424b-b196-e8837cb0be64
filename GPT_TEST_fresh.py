import pandas as pd
import re

# Load your Excel file
input_file = "GPT TEST.xlsx"
df = pd.read_excel(input_file, sheet_name="Sheet1")

# Mapping dictionary
mapping = {}
token_counters = {"M": 1, "A": 1, "S": 1}

def mask_text(text, prefix):
    """Mask LaTeX/math expressions in text with tokens"""
    if not isinstance(text, str):
        return text

    # Store original text for reference
    original_text = text
    masked_text = text

    # Define patterns in order of priority (most specific first)
    patterns = [
        # Complete integral expressions (highest priority)
        r"∫[^∫\n]*?(?:dx|dy|dz|dt|dθ|du|dv|dw)",

        # LaTeX commands with their complete arguments
        r"\\int\\frac\{[^}]*\}\{[^}]*\}d\\theta",
        r"\\frac\{[^}]*\}\{[^}]*\}",
        r"\\sqrt\{[^}]*\}",
        r"\\[a-zA-Z]+\{[^}]*\}",
        r"\\[a-zA-Z]+",

        # Mathematical expressions with superscripts/subscripts
        r"[a-zA-Z0-9]+\^[a-zA-Z0-9⁄()]+",
        r"[a-zA-Z0-9]+_[a-zA-Z0-9⁄()]+",
        r"[a-zA-Z0-9]+[²³⁴⁵⁶⁷⁸⁹⁰¹]+",
        r"[a-zA-Z0-9]+[₀₁₂₃₄₅₆₇₈₉]+",

        # Fractions
        r"[0-9]+⁄[0-9]+",

        # Square roots with Unicode
        r"√[a-zA-Z0-9]+",

        # Mathematical symbols and special characters
        r"[ℝ∞±∉]",
    ]

    # Apply patterns one by one, being careful not to re-process already masked content
    for pattern in patterns:
        # Find all matches in the current state of masked_text
        matches = []
        for match in re.finditer(pattern, masked_text):
            match_text = match.group()
            # Skip if this is already a token
            if not (match_text.startswith('<') and match_text.endswith('>')):
                matches.append((match.start(), match.end(), match_text))

        # Replace matches from right to left to preserve positions
        for start, end, match_text in reversed(matches):
            token = f"<{prefix}{token_counters[prefix]}>"
            mapping[token] = match_text
            masked_text = masked_text[:start] + token + masked_text[end:]
            token_counters[prefix] += 1

    return masked_text

# Create a copy of the original dataframe
df_result = df.copy()

# Process each column separately and check results
print("Processing Question column...")
masked_questions = []
for i, question in enumerate(df["Question"]):
    if pd.isna(question):
        masked_questions.append(question)
    else:
        masked = mask_text(question, "M")
        masked_questions.append(masked)
        if i >= 11 and i <= 15:  # rows 12-16
            print(f"  Row {i+1}: {type(masked)} - {repr(masked[:50])}")

df_result["Masked Question"] = masked_questions

print("\nProcessing Answer Key column...")
masked_answers = []
for i, answer in enumerate(df["Answer_key"]):
    if pd.isna(answer):
        masked_answers.append(answer)
    else:
        masked = mask_text(answer, "A")
        masked_answers.append(masked)
        if i >= 11 and i <= 15:  # rows 12-16
            print(f"  Row {i+1}: {type(masked)} - {repr(masked[:50])}")

df_result["Masked Answer Key"] = masked_answers

print("\nProcessing Solution column...")
masked_solutions = []
for i, solution in enumerate(df["Solution"]):
    if pd.isna(solution):
        masked_solutions.append(solution)
        if i >= 11 and i <= 15:  # rows 12-16
            print(f"  Row {i+1}: NaN - keeping as NaN")
    else:
        masked = mask_text(solution, "S")
        masked_solutions.append(masked)
        if i >= 11 and i <= 15:  # rows 12-16
            print(f"  Row {i+1}: {type(masked)} - {repr(masked[:50])}")

df_result["Masked Solution"] = masked_solutions

# Prepare final dataset
df_masked = df_result[[
    "Question ID", "Subject", "Chapter name", "Topic",
    "Masked Question", "Masked Answer Key", "Masked Solution",
    "Question Image tag / Url", "Solution Image tag / Url"
]]

# Create mapping table
df_mapping = pd.DataFrame(list(mapping.items()), columns=["Token", "Original Math Expression"])

# Save to Excel with two sheets
output_file = "masked_GPT_TEST_fresh.xlsx"
with pd.ExcelWriter(output_file, engine="openpyxl") as writer:
    df_masked.to_excel(writer, index=False, sheet_name="Masked Dataset")
    df_mapping.to_excel(writer, index=False, sheet_name="Mapping Table")

print(f"\nMasked file created: {output_file}")

# Final verification
print("\nFinal verification of rows 12-16:")
for i in range(11, min(16, len(df_masked))):
    row_num = i + 1
    solution = df_masked.iloc[i]["Masked Solution"]
    print(f"Row {row_num}: Type={type(solution)}, IsNaN={pd.isna(solution)}")
    if not pd.isna(solution):
        print(f"  Content: {repr(solution[:50])}")
