#!/usr/bin/env python3
"""
Mathematical Expression Masking Tool - Launcher
===============================================

This script launches the Mathematical Expression Masking Tool UI.

Features:
- Mask mathematical expressions in datasets
- Restore original expressions from tokens
- User-friendly graphical interface
- Support for Excel and CSV files

Usage:
    python launch_ui.py

Requirements:
    - Python 3.6+
    - pandas
    - tkinter (usually included with Python)
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from math_masking_ui import main
    print("Starting Mathematical Expression Masking Tool...")
    main()
except ImportError as e:
    print(f"Error importing required modules: {e}")
    print("Please make sure you have pandas installed:")
    print("  pip install pandas")
    sys.exit(1)
except Exception as e:
    print(f"Error starting application: {e}")
    sys.exit(1)
