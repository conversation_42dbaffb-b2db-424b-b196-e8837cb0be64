import pandas as pd

# Load the restored file
df_restored = pd.read_csv("test_cli_dataset_RESTORED.csv")

print("=== RESTORATION VERIFICATION ===")
print("Checking rows 12-16 (the problematic ones):")

for i in range(11, min(16, len(df_restored))):
    row_num = i + 1
    print(f"\n--- ROW {row_num} ---")
    
    # Show masked version
    masked_solution = df_restored.iloc[i]['Masked Solution']
    print(f"Masked:   {repr(masked_solution[:80])}...")
    
    # Show restored version
    restored_solution = df_restored.iloc[i]['Restored Solution']
    print(f"Restored: {repr(restored_solution[:80])}...")
    
    # Check if restoration worked
    if pd.isna(masked_solution):
        print("Status: ❌ Masked solution was NaN")
    elif pd.isna(restored_solution):
        print("Status: ❌ Restoration failed")
    elif '<' in restored_solution and '>' in restored_solution:
        print("Status: ⚠️ Some tokens not restored")
    else:
        print("Status: ✅ Successfully restored")

print(f"\n=== SUMMARY ===")
print(f"Total rows: {len(df_restored)}")

# Check how many solutions were successfully restored
masked_solutions = df_restored['Masked Solution'].notna().sum()
restored_solutions = df_restored['Restored Solution'].notna().sum()

print(f"Masked solutions: {masked_solutions}")
print(f"Restored solutions: {restored_solutions}")
print(f"Restoration success rate: {(restored_solutions/masked_solutions)*100:.1f}%")

# Check for any remaining tokens
remaining_tokens = 0
for text in df_restored['Restored Solution']:
    if isinstance(text, str) and '<' in text and '>' in text:
        remaining_tokens += 1

if remaining_tokens == 0:
    print("🎉 PERFECT! All tokens successfully restored!")
else:
    print(f"⚠️ {remaining_tokens} rows still contain unreplaced tokens")
