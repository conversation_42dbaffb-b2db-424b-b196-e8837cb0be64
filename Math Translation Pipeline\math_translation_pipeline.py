#!/usr/bin/env python3
"""
Mathematical Expression Translation Pipeline
==========================================

A comprehensive tool for translating educational content while preserving mathematical expressions.

Workflow: Mask → Translate → Restore

Phase 1: Masking - Replace LaTeX/math with tokens
Phase 2: Translation - Translate text while preserving tokens  
Phase 3: Restoration - Replace tokens with original math expressions

Author: AI Assistant
Version: 1.0
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import pandas as pd
import re
import os
import json
import requests
import time
from pathlib import Path
from datetime import datetime

class MathTranslationPipeline:
    def __init__(self, root):
        self.root = root
        self.root.title("Mathematical Expression Translation Pipeline")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)
        
        # Variables
        self.input_file = tk.StringVar()
        self.output_folder = tk.StringVar(value="output")
        self.project_name = tk.StringVar(value="translation_project")
        self.source_lang = tk.StringVar(value="en")
        self.target_lang = tk.StringVar(value="ur")
        self.translation_service = tk.StringVar(value="deepl")
        self.deepl_api_key = tk.StringVar()
        self.bhashaindia_api_key = tk.StringVar()
        
        # Data storage
        self.mapping = {}
        self.token_counter = 1
        self.current_project_path = None
        
        # Supported languages
        self.languages = {
            "English": "en", "Urdu": "ur", "Arabic": "ar", "Hindi": "hi",
            "Spanish": "es", "French": "fr", "German": "de", "Chinese": "zh",
            "Japanese": "ja", "Korean": "ko", "Russian": "ru", "Portuguese": "pt",
            "Bengali": "bn", "Tamil": "ta", "Telugu": "te", "Malayalam": "ml",
            "Kannada": "kn", "Gujarati": "gu", "Punjabi": "pa", "Odia": "or",
            "Assamese": "as", "Marathi": "mr", "Bahasa Indonesia": "id"
        }
        
        self.create_widgets()
        self.load_settings()
        
    def create_widgets(self):
        # Create main notebook
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create tabs
        self.create_setup_tab()
        self.create_masking_tab()
        self.create_translation_tab()
        self.create_restoration_tab()
        self.create_settings_tab()
        
    def create_setup_tab(self):
        """Project setup and configuration"""
        setup_frame = ttk.Frame(self.notebook)
        self.notebook.add(setup_frame, text="📋 Project Setup")
        
        # Title
        title_label = ttk.Label(setup_frame, text="Mathematical Expression Translation Pipeline", 
                               font=("Arial", 18, "bold"))
        title_label.pack(pady=15)
        
        subtitle_label = ttk.Label(setup_frame, text="Mask → Translate → Restore", 
                                  font=("Arial", 12, "italic"))
        subtitle_label.pack(pady=5)
        
        # Project configuration
        config_frame = ttk.LabelFrame(setup_frame, text="Project Configuration", padding=15)
        config_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # Project name
        ttk.Label(config_frame, text="Project Name:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Entry(config_frame, textvariable=self.project_name, width=30).grid(row=0, column=1, sticky=tk.W, padx=10)
        
        # Input file
        ttk.Label(config_frame, text="Input File:").grid(row=1, column=0, sticky=tk.W, pady=5)
        file_frame = ttk.Frame(config_frame)
        file_frame.grid(row=1, column=1, columnspan=2, sticky=tk.W+tk.E, padx=10)
        ttk.Entry(file_frame, textvariable=self.input_file, width=40).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(file_frame, text="Browse", command=self.browse_input_file).pack(side=tk.RIGHT, padx=(5,0))
        
        # Output folder
        ttk.Label(config_frame, text="Output Folder:").grid(row=2, column=0, sticky=tk.W, pady=5)
        output_frame = ttk.Frame(config_frame)
        output_frame.grid(row=2, column=1, columnspan=2, sticky=tk.W+tk.E, padx=10)
        ttk.Entry(output_frame, textvariable=self.output_folder, width=40).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(output_frame, text="Browse", command=self.browse_output_folder).pack(side=tk.RIGHT, padx=(5,0))
        
        # Language configuration
        lang_frame = ttk.LabelFrame(setup_frame, text="Language Configuration", padding=15)
        lang_frame.pack(fill=tk.X, padx=20, pady=10)
        
        ttk.Label(lang_frame, text="Source Language:").grid(row=0, column=0, sticky=tk.W, pady=5)
        source_combo = ttk.Combobox(lang_frame, textvariable=self.source_lang, values=list(self.languages.keys()), width=15)
        source_combo.grid(row=0, column=1, sticky=tk.W, padx=10)
        source_combo.set("English")
        
        ttk.Label(lang_frame, text="Target Language:").grid(row=0, column=2, sticky=tk.W, pady=5, padx=(20,0))
        target_combo = ttk.Combobox(lang_frame, textvariable=self.target_lang, values=list(self.languages.keys()), width=15)
        target_combo.grid(row=0, column=3, sticky=tk.W, padx=10)
        target_combo.set("Urdu")
        
        # Translation service
        service_frame = ttk.LabelFrame(setup_frame, text="Translation Service", padding=15)
        service_frame.pack(fill=tk.X, padx=20, pady=10)
        
        ttk.Radiobutton(service_frame, text="DeepL (Recommended)", variable=self.translation_service,
                       value="deepl").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Radiobutton(service_frame, text="Google Translate", variable=self.translation_service,
                       value="google").grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Radiobutton(service_frame, text="BhashaIndia", variable=self.translation_service,
                       value="bhashaindia").grid(row=2, column=0, sticky=tk.W, pady=2)

        ttk.Label(service_frame, text="DeepL API Key:").grid(row=3, column=0, sticky=tk.W, pady=5)
        ttk.Entry(service_frame, textvariable=self.deepl_api_key, width=40, show="*").grid(row=3, column=1, sticky=tk.W, padx=10)

        ttk.Label(service_frame, text="BhashaIndia API Key:").grid(row=4, column=0, sticky=tk.W, pady=5)
        ttk.Entry(service_frame, textvariable=self.bhashaindia_api_key, width=40, show="*").grid(row=4, column=1, sticky=tk.W, padx=10)
        
        # Action buttons
        button_frame = ttk.Frame(setup_frame)
        button_frame.pack(pady=20)
        
        ttk.Button(button_frame, text="🚀 Start Pipeline", command=self.start_pipeline, 
                  style="Accent.TButton").pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="💾 Save Settings", command=self.save_settings).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="📁 Load Project", command=self.load_project).pack(side=tk.LEFT, padx=5)
        
        # Status
        self.setup_status = ttk.Label(setup_frame, text="Ready to start", foreground="green")
        self.setup_status.pack(pady=10)
        
    def create_masking_tab(self):
        """Phase 1: Masking mathematical expressions"""
        mask_frame = ttk.Frame(self.notebook)
        self.notebook.add(mask_frame, text="🎭 Phase 1: Masking")
        
        # Title
        title_label = ttk.Label(mask_frame, text="Phase 1: Mathematical Expression Masking", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        description = ttk.Label(mask_frame, text="Replace LaTeX and mathematical symbols with unique tokens", 
                               font=("Arial", 10))
        description.pack(pady=5)
        
        # Configuration
        config_frame = ttk.LabelFrame(mask_frame, text="Masking Configuration", padding=10)
        config_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # Token format
        ttk.Label(config_frame, text="Token Format:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.token_format = tk.StringVar(value="[MATH_{:03d}]")
        ttk.Entry(config_frame, textvariable=self.token_format, width=20).grid(row=0, column=1, sticky=tk.W, padx=10)
        ttk.Label(config_frame, text="Example: [MATH_001]").grid(row=0, column=2, sticky=tk.W, padx=10)
        
        # Process button
        process_frame = ttk.Frame(mask_frame)
        process_frame.pack(pady=15)
        
        ttk.Button(process_frame, text="🎭 Start Masking Process", 
                  command=self.process_masking, style="Accent.TButton").pack()
        
        # Progress
        self.mask_progress = ttk.Progressbar(mask_frame, mode='indeterminate')
        self.mask_progress.pack(fill=tk.X, padx=20, pady=5)
        
        # Results
        results_frame = ttk.LabelFrame(mask_frame, text="Masking Results", padding=10)
        results_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        self.mask_results = scrolledtext.ScrolledText(results_frame, height=15, wrap=tk.WORD)
        self.mask_results.pack(fill=tk.BOTH, expand=True)
        
    def create_translation_tab(self):
        """Phase 2: Translation with token preservation"""
        trans_frame = ttk.Frame(self.notebook)
        self.notebook.add(trans_frame, text="🌐 Phase 2: Translation")
        
        # Title
        title_label = ttk.Label(trans_frame, text="Phase 2: Safe Language Translation", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        description = ttk.Label(trans_frame, text="Translate text while preserving mathematical tokens", 
                               font=("Arial", 10))
        description.pack(pady=5)
        
        # Translation settings
        settings_frame = ttk.LabelFrame(trans_frame, text="Translation Settings", padding=10)
        settings_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # Batch size
        ttk.Label(settings_frame, text="Batch Size:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.batch_size = tk.IntVar(value=10)
        ttk.Spinbox(settings_frame, from_=1, to=100, textvariable=self.batch_size, width=10).grid(row=0, column=1, sticky=tk.W, padx=10)
        
        # Delay between requests
        ttk.Label(settings_frame, text="Delay (seconds):").grid(row=0, column=2, sticky=tk.W, pady=5, padx=(20,0))
        self.request_delay = tk.DoubleVar(value=1.0)
        ttk.Spinbox(settings_frame, from_=0.1, to=10.0, increment=0.1, textvariable=self.request_delay, width=10).grid(row=0, column=3, sticky=tk.W, padx=10)
        
        # Process button
        process_frame = ttk.Frame(trans_frame)
        process_frame.pack(pady=15)
        
        ttk.Button(process_frame, text="🌐 Start Translation Process", 
                  command=self.process_translation, style="Accent.TButton").pack()
        
        # Progress
        self.trans_progress = ttk.Progressbar(trans_frame, mode='determinate')
        self.trans_progress.pack(fill=tk.X, padx=20, pady=5)
        
        # Results
        results_frame = ttk.LabelFrame(trans_frame, text="Translation Results", padding=10)
        results_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        self.trans_results = scrolledtext.ScrolledText(results_frame, height=15, wrap=tk.WORD)
        self.trans_results.pack(fill=tk.BOTH, expand=True)

    def create_restoration_tab(self):
        """Phase 3: Restoration of mathematical expressions"""
        restore_frame = ttk.Frame(self.notebook)
        self.notebook.add(restore_frame, text="🔁 Phase 3: Restoration")

        # Title
        title_label = ttk.Label(restore_frame, text="Phase 3: Mathematical Expression Restoration",
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=10)

        description = ttk.Label(restore_frame, text="Replace tokens with original LaTeX expressions",
                               font=("Arial", 10))
        description.pack(pady=5)

        # Validation options
        validation_frame = ttk.LabelFrame(restore_frame, text="Validation Options", padding=10)
        validation_frame.pack(fill=tk.X, padx=20, pady=10)

        self.validate_restoration = tk.BooleanVar(value=True)
        ttk.Checkbutton(validation_frame, text="Validate restoration completeness",
                       variable=self.validate_restoration).pack(anchor=tk.W, pady=2)

        self.create_comparison = tk.BooleanVar(value=True)
        ttk.Checkbutton(validation_frame, text="Create before/after comparison",
                       variable=self.create_comparison).pack(anchor=tk.W, pady=2)

        # Process button
        process_frame = ttk.Frame(restore_frame)
        process_frame.pack(pady=15)

        ttk.Button(process_frame, text="🔁 Start Restoration Process",
                  command=self.process_restoration, style="Accent.TButton").pack()

        # Progress
        self.restore_progress = ttk.Progressbar(restore_frame, mode='indeterminate')
        self.restore_progress.pack(fill=tk.X, padx=20, pady=5)

        # Results
        results_frame = ttk.LabelFrame(restore_frame, text="Restoration Results", padding=10)
        results_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        self.restore_results = scrolledtext.ScrolledText(results_frame, height=15, wrap=tk.WORD)
        self.restore_results.pack(fill=tk.BOTH, expand=True)

    def create_settings_tab(self):
        """Settings and configuration"""
        settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(settings_frame, text="⚙️ Settings")

        # Title
        title_label = ttk.Label(settings_frame, text="Application Settings",
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=10)

        # API Configuration
        api_frame = ttk.LabelFrame(settings_frame, text="API Configuration", padding=15)
        api_frame.pack(fill=tk.X, padx=20, pady=10)

        ttk.Label(api_frame, text="DeepL API Key:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Entry(api_frame, textvariable=self.deepl_api_key, width=50, show="*").grid(row=0, column=1, sticky=tk.W, padx=10)

        ttk.Label(api_frame, text="BhashaIndia API Key:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(api_frame, textvariable=self.bhashaindia_api_key, width=50, show="*").grid(row=1, column=1, sticky=tk.W, padx=10)

        ttk.Label(api_frame, text="DeepL: https://www.deepl.com/pro-api",
                 foreground="blue").grid(row=2, column=1, sticky=tk.W, padx=10, pady=2)
        ttk.Label(api_frame, text="BhashaIndia: https://bhashaindia.com/api",
                 foreground="blue").grid(row=3, column=1, sticky=tk.W, padx=10, pady=2)

        # Advanced Settings
        advanced_frame = ttk.LabelFrame(settings_frame, text="Advanced Settings", padding=15)
        advanced_frame.pack(fill=tk.X, padx=20, pady=10)

        # Backup options
        self.create_backups = tk.BooleanVar(value=True)
        ttk.Checkbutton(advanced_frame, text="Create backup files",
                       variable=self.create_backups).grid(row=0, column=0, sticky=tk.W, pady=2)

        self.detailed_logging = tk.BooleanVar(value=True)
        ttk.Checkbutton(advanced_frame, text="Enable detailed logging",
                       variable=self.detailed_logging).grid(row=1, column=0, sticky=tk.W, pady=2)

        # File format preferences
        ttk.Label(advanced_frame, text="Output Format:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.output_format = tk.StringVar(value="csv")
        format_frame = ttk.Frame(advanced_frame)
        format_frame.grid(row=2, column=1, sticky=tk.W, padx=10)
        ttk.Radiobutton(format_frame, text="CSV", variable=self.output_format, value="csv").pack(side=tk.LEFT)
        ttk.Radiobutton(format_frame, text="Excel", variable=self.output_format, value="xlsx").pack(side=tk.LEFT, padx=10)

        # Action buttons
        button_frame = ttk.Frame(settings_frame)
        button_frame.pack(pady=20)

        ttk.Button(button_frame, text="💾 Save Settings", command=self.save_settings).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🔄 Reset to Defaults", command=self.reset_settings).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="📋 Test API Connection", command=self.test_api_connection).pack(side=tk.LEFT, padx=5)

        # About section
        about_frame = ttk.LabelFrame(settings_frame, text="About", padding=15)
        about_frame.pack(fill=tk.X, padx=20, pady=10)

        about_text = """Mathematical Expression Translation Pipeline v1.0

This tool enables safe translation of educational content containing mathematical expressions.

Workflow:
1. 🎭 Masking: Replace LaTeX/math with tokens
2. 🌐 Translation: Translate text while preserving tokens
3. 🔁 Restoration: Replace tokens with original expressions

Supported formats: Excel (.xlsx), CSV (.csv)
Translation services: DeepL, Google Translate, BhashaIndia

BhashaIndia specializes in Indian language translations with support for:
Hindi, Urdu, Bengali, Tamil, Telugu, Malayalam, Kannada, Gujarati,
Punjabi, Odia, Assamese, Marathi, and more.

Also supports: Bahasa Indonesia for Indonesian translations.
"""

        about_label = tk.Text(about_frame, height=8, wrap=tk.WORD)
        about_label.insert(tk.END, about_text)
        about_label.config(state=tk.DISABLED)
        about_label.pack(fill=tk.X)

    # ============================================================================
    # CORE FUNCTIONALITY METHODS
    # ============================================================================

    def browse_input_file(self):
        """Browse for input file"""
        filename = filedialog.askopenfilename(
            title="Select input file",
            filetypes=[("Excel files", "*.xlsx"), ("CSV files", "*.csv"), ("All files", "*.*")]
        )
        if filename:
            self.input_file.set(filename)

    def browse_output_folder(self):
        """Browse for output folder"""
        folder = filedialog.askdirectory(title="Select output folder")
        if folder:
            self.output_folder.set(folder)

    def save_settings(self):
        """Save current settings to file"""
        settings = {
            'deepl_api_key': self.deepl_api_key.get(),
            'bhashaindia_api_key': self.bhashaindia_api_key.get(),
            'output_folder': self.output_folder.get(),
            'source_lang': self.source_lang.get(),
            'target_lang': self.target_lang.get(),
            'translation_service': self.translation_service.get(),
            'token_format': self.token_format.get(),
            'batch_size': self.batch_size.get(),
            'request_delay': self.request_delay.get(),
            'create_backups': self.create_backups.get(),
            'detailed_logging': self.detailed_logging.get(),
            'output_format': self.output_format.get()
        }

        try:
            with open('settings.json', 'w') as f:
                json.dump(settings, f, indent=2)
            messagebox.showinfo("Success", "Settings saved successfully!")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save settings: {e}")

    def load_settings(self):
        """Load settings from file"""
        try:
            if os.path.exists('settings.json'):
                with open('settings.json', 'r') as f:
                    settings = json.load(f)

                self.deepl_api_key.set(settings.get('deepl_api_key', ''))
                self.bhashaindia_api_key.set(settings.get('bhashaindia_api_key', ''))
                self.output_folder.set(settings.get('output_folder', 'output'))
                self.source_lang.set(settings.get('source_lang', 'en'))
                self.target_lang.set(settings.get('target_lang', 'ur'))
                self.translation_service.set(settings.get('translation_service', 'deepl'))
                self.token_format.set(settings.get('token_format', '[MATH_{:03d}]'))
                self.batch_size.set(settings.get('batch_size', 10))
                self.request_delay.set(settings.get('request_delay', 1.0))
                self.create_backups.set(settings.get('create_backups', True))
                self.detailed_logging.set(settings.get('detailed_logging', True))
                self.output_format.set(settings.get('output_format', 'csv'))
        except Exception as e:
            print(f"Failed to load settings: {e}")

    def reset_settings(self):
        """Reset settings to defaults"""
        if messagebox.askyesno("Confirm", "Reset all settings to defaults?"):
            self.deepl_api_key.set('')
            self.bhashaindia_api_key.set('')
            self.output_folder.set('output')
            self.source_lang.set('en')
            self.target_lang.set('ur')
            self.translation_service.set('deepl')
            self.token_format.set('[MATH_{:03d}]')
            self.batch_size.set(10)
            self.request_delay.set(1.0)
            self.create_backups.set(True)
            self.detailed_logging.set(True)
            self.output_format.set('csv')

    def test_api_connection(self):
        """Test API connection"""
        if self.translation_service.get() == 'deepl':
            if not self.deepl_api_key.get():
                messagebox.showerror("Error", "Please enter DeepL API key")
                return

            try:
                # Test DeepL API
                url = "https://api-free.deepl.com/v2/translate"
                headers = {"Authorization": f"DeepL-Auth-Key {self.deepl_api_key.get()}"}
                data = {"text": "Hello", "target_lang": "DE"}

                response = requests.post(url, headers=headers, data=data, timeout=10)
                if response.status_code == 200:
                    messagebox.showinfo("Success", "DeepL API connection successful!")
                else:
                    messagebox.showerror("Error", f"API Error: {response.status_code}")
            except Exception as e:
                messagebox.showerror("Error", f"Connection failed: {e}")
        elif self.translation_service.get() == 'bhashaindia':
            if not self.bhashaindia_api_key.get():
                messagebox.showerror("Error", "Please enter BhashaIndia API key")
                return

            try:
                # Test BhashaIndia API
                url = "https://api.bhashaindia.com/translate"
                headers = {"Authorization": f"Bearer {self.bhashaindia_api_key.get()}"}
                data = {"text": "Hello", "source": "en", "target": "hi"}

                response = requests.post(url, headers=headers, json=data, timeout=10)
                if response.status_code == 200:
                    messagebox.showinfo("Success", "BhashaIndia API connection successful!")
                else:
                    messagebox.showerror("Error", f"API Error: {response.status_code}")
            except Exception as e:
                messagebox.showerror("Error", f"Connection failed: {e}")
        else:
            messagebox.showinfo("Info", "Google Translate doesn't require API key testing")

    def load_project(self):
        """Load existing project"""
        folder = filedialog.askdirectory(title="Select project folder")
        if folder:
            project_file = os.path.join(folder, "project_info.json")
            if os.path.exists(project_file):
                try:
                    with open(project_file, 'r') as f:
                        project_info = json.load(f)

                    self.project_name.set(project_info.get('name', ''))
                    self.input_file.set(project_info.get('input_file', ''))
                    self.output_folder.set(folder)
                    self.current_project_path = folder

                    messagebox.showinfo("Success", f"Project '{project_info.get('name')}' loaded!")
                except Exception as e:
                    messagebox.showerror("Error", f"Failed to load project: {e}")
            else:
                messagebox.showerror("Error", "No project file found in selected folder")

    # ============================================================================
    # PHASE 1: MASKING FUNCTIONALITY
    # ============================================================================

    def mask_mathematical_expressions(self, text):
        """Mask mathematical expressions with tokens"""
        if not isinstance(text, str) or pd.isna(text):
            return text, {}

        local_mapping = {}
        masked_text = text

        # Define patterns for mathematical expressions (ordered by priority)
        patterns = [
            # LaTeX expressions
            (r'\\frac\{[^}]*\}\{[^}]*\}', 'LaTeX fraction'),
            (r'\\sqrt\{[^}]*\}', 'LaTeX square root'),
            (r'\\int\{[^}]*\}', 'LaTeX integral'),
            (r'\\sum\{[^}]*\}', 'LaTeX summation'),
            (r'\\prod\{[^}]*\}', 'LaTeX product'),
            (r'\\lim\{[^}]*\}', 'LaTeX limit'),
            (r'\\[a-zA-Z]+\{[^}]*\}', 'LaTeX command with braces'),
            (r'\\[a-zA-Z]+', 'LaTeX command'),

            # Complex mathematical expressions
            (r'∫[^∫\n]*?(?:dx|dy|dz|dt|dθ|du|dv|dw)', 'Integral expression'),
            (r'∑[^∑\n]*?(?:=|to|\d)', 'Summation expression'),
            (r'∏[^∏\n]*?(?:=|to|\d)', 'Product expression'),

            # Unicode mathematical symbols and expressions
            (r'[a-zA-Z0-9]+[²³⁴⁵⁶⁷⁸⁹⁰¹]+', 'Superscript expression'),
            (r'[a-zA-Z0-9]+[₀₁₂₃₄₅₆₇₈₉]+', 'Subscript expression'),
            (r'[a-zA-Z0-9]+\^[a-zA-Z0-9⁄(){}]+', 'Caret superscript'),
            (r'[a-zA-Z0-9]+_[a-zA-Z0-9⁄(){}]+', 'Underscore subscript'),

            # Fractions and roots
            (r'[0-9]+⁄[0-9]+', 'Unicode fraction'),
            (r'√[a-zA-Z0-9]+', 'Unicode square root'),

            # Mathematical symbols
            (r'[∫∑∏∆∇∂∞±≤≥≠≈∈∉⊂⊃∪∩∧∨¬∀∃ℝℕℤℚℂ]', 'Mathematical symbol'),
            (r'[αβγδεζηθικλμνξοπρστυφχψω]', 'Greek letter'),
            (r'[ΑΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡΣΤΥΦΧΨΩ]', 'Greek capital letter'),
        ]

        # Apply patterns
        for pattern, description in patterns:
            matches = []
            for match in re.finditer(pattern, masked_text):
                match_text = match.group()
                # Skip if already a token
                if not (match_text.startswith('[') and match_text.endswith(']')):
                    matches.append((match.start(), match.end(), match_text, description))

            # Replace matches from right to left to preserve positions
            for start, end, match_text, desc in reversed(matches):
                token = self.token_format.get().format(self.token_counter)
                local_mapping[token] = {
                    'original': match_text,
                    'description': desc,
                    'position': start
                }
                masked_text = masked_text[:start] + token + masked_text[end:]
                self.token_counter += 1

        return masked_text, local_mapping

    def process_masking(self):
        """Process the masking phase"""
        if not self.input_file.get():
            messagebox.showerror("Error", "Please select an input file")
            return

        try:
            self.mask_progress.start()
            self.mask_results.delete(1.0, tk.END)
            self.log_message("🎭 Starting Phase 1: Mathematical Expression Masking", self.mask_results)

            # Create project folder
            project_folder = self.create_project_folder()

            # Load input file
            self.log_message(f"📂 Loading input file: {self.input_file.get()}", self.mask_results)

            if self.input_file.get().endswith('.xlsx'):
                df = pd.read_excel(self.input_file.get())
            else:
                df = pd.read_csv(self.input_file.get())

            self.log_message(f"✅ Loaded {len(df)} rows", self.mask_results)

            # Reset counters
            self.mapping = {}
            self.token_counter = 1

            # Process each column
            df_masked = df.copy()
            columns_to_process = ['Question', 'Answer_key', 'Solution']

            for column in columns_to_process:
                if column in df.columns:
                    self.log_message(f"🔍 Processing {column} column...", self.mask_results)

                    masked_data = []
                    for i, text in enumerate(df[column]):
                        masked_text, local_mapping = self.mask_mathematical_expressions(text)
                        masked_data.append(masked_text)

                        # Merge local mapping into global mapping
                        self.mapping.update(local_mapping)

                        # Update progress
                        if i % 10 == 0:
                            self.root.update()

                    df_masked[f'Masked_{column}'] = masked_data
                    self.log_message(f"✅ {column} column processed", self.mask_results)

            # Save masked dataset
            masked_file = os.path.join(project_folder, f"{self.project_name.get()}_masked.{self.output_format.get()}")
            if self.output_format.get() == 'xlsx':
                df_masked.to_excel(masked_file, index=False)
            else:
                df_masked.to_csv(masked_file, index=False)

            self.log_message(f"💾 Masked dataset saved: {masked_file}", self.mask_results)

            # Save mapping table
            mapping_data = []
            for token, info in self.mapping.items():
                mapping_data.append({
                    'Token': token,
                    'Original_Expression': info['original'],
                    'Description': info['description'],
                    'Position': info['position']
                })

            df_mapping = pd.DataFrame(mapping_data)
            mapping_file = os.path.join(project_folder, f"{self.project_name.get()}_mapping.csv")
            df_mapping.to_csv(mapping_file, index=False)

            self.log_message(f"🗺️ Mapping table saved: {mapping_file}", self.mask_results)

            # Summary
            self.log_message(f"\n📊 MASKING SUMMARY:", self.mask_results)
            self.log_message(f"   • Total rows processed: {len(df)}", self.mask_results)
            self.log_message(f"   • Mathematical expressions found: {len(self.mapping)}", self.mask_results)
            self.log_message(f"   • Columns processed: {len([c for c in columns_to_process if c in df.columns])}", self.mask_results)
            self.log_message(f"   • Files created: 2 (masked dataset + mapping table)", self.mask_results)

            messagebox.showinfo("Success", f"Masking completed!\n\n{len(self.mapping)} mathematical expressions masked\nFiles saved to: {project_folder}")

        except Exception as e:
            self.log_message(f"❌ ERROR: {str(e)}", self.mask_results)
            messagebox.showerror("Error", f"Masking failed: {str(e)}")
        finally:
            self.mask_progress.stop()

    # ============================================================================
    # PHASE 2: TRANSLATION FUNCTIONALITY
    # ============================================================================

    def translate_with_deepl(self, text, source_lang, target_lang):
        """Translate text using DeepL API"""
        if not self.deepl_api_key.get():
            raise ValueError("DeepL API key not provided")

        url = "https://api-free.deepl.com/v2/translate"
        headers = {"Authorization": f"DeepL-Auth-Key {self.deepl_api_key.get()}"}

        # Map language codes
        lang_mapping = {
            'en': 'EN', 'ur': 'UR', 'ar': 'AR', 'hi': 'HI',
            'es': 'ES', 'fr': 'FR', 'de': 'DE', 'zh': 'ZH',
            'ja': 'JA', 'ko': 'KO', 'ru': 'RU', 'pt': 'PT',
            'id': 'ID', 'bn': 'BN', 'ta': 'TA', 'te': 'TE',
            'ml': 'ML', 'kn': 'KN', 'gu': 'GU', 'pa': 'PA',
            'or': 'OR', 'as': 'AS', 'mr': 'MR'
        }

        target_code = lang_mapping.get(target_lang, target_lang.upper())

        data = {
            "text": text,
            "target_lang": target_code,
            "preserve_formatting": "1",
            "tag_handling": "xml"
        }

        if source_lang != 'auto':
            data["source_lang"] = lang_mapping.get(source_lang, source_lang.upper())

        response = requests.post(url, headers=headers, data=data, timeout=30)

        if response.status_code == 200:
            result = response.json()
            return result['translations'][0]['text']
        else:
            raise Exception(f"DeepL API error: {response.status_code} - {response.text}")

    def translate_with_google(self, text, source_lang, target_lang):
        """Translate text using Google Translate (free, no API key required)"""
        try:
            from googletrans import Translator
            translator = Translator()

            # Map language codes
            lang_mapping = {
                'en': 'en', 'ur': 'ur', 'ar': 'ar', 'hi': 'hi',
                'es': 'es', 'fr': 'fr', 'de': 'de', 'zh': 'zh',
                'ja': 'ja', 'ko': 'ko', 'ru': 'ru', 'pt': 'pt',
                'id': 'id', 'bn': 'bn', 'ta': 'ta', 'te': 'te',
                'ml': 'ml', 'kn': 'kn', 'gu': 'gu', 'pa': 'pa',
                'or': 'or', 'as': 'as', 'mr': 'mr'
            }

            src = lang_mapping.get(source_lang, source_lang)
            dest = lang_mapping.get(target_lang, target_lang)

            result = translator.translate(text, src=src, dest=dest)
            return result.text

        except ImportError:
            raise Exception("googletrans library not installed. Install with: pip install googletrans==4.0.0-rc1")
        except Exception as e:
            raise Exception(f"Google Translate error: {str(e)}")

    def translate_with_bhashaindia(self, text, source_lang, target_lang):
        """Translate text using BhashaIndia API"""
        if not self.bhashaindia_api_key.get():
            raise ValueError("BhashaIndia API key not provided")

        url = "https://api.bhashaindia.com/translate"
        headers = {
            "Authorization": f"Bearer {self.bhashaindia_api_key.get()}",
            "Content-Type": "application/json"
        }

        # Map language codes for BhashaIndia
        lang_mapping = {
            'en': 'en', 'ur': 'ur', 'ar': 'ar', 'hi': 'hi',
            'es': 'es', 'fr': 'fr', 'de': 'de', 'zh': 'zh',
            'ja': 'ja', 'ko': 'ko', 'ru': 'ru', 'pt': 'pt',
            'id': 'id', 'bn': 'bn', 'ta': 'ta', 'te': 'te',
            'ml': 'ml', 'kn': 'kn', 'gu': 'gu', 'pa': 'pa',
            'or': 'or', 'as': 'as', 'mr': 'mr'
        }

        source_code = lang_mapping.get(source_lang, source_lang)
        target_code = lang_mapping.get(target_lang, target_lang)

        data = {
            "text": text,
            "source": source_code,
            "target": target_code,
            "preserve_formatting": True
        }

        response = requests.post(url, headers=headers, json=data, timeout=30)

        if response.status_code == 200:
            result = response.json()
            return result.get('translated_text', text)
        else:
            raise Exception(f"BhashaIndia API error: {response.status_code} - {response.text}")

    def translate_text(self, text, source_lang, target_lang):
        """Translate text using selected service"""
        if not isinstance(text, str) or pd.isna(text) or not text.strip():
            return text

        # Skip if text contains only tokens
        if re.match(r'^[\[\]A-Z_0-9\s]+$', text):
            return text

        if self.translation_service.get() == 'deepl':
            return self.translate_with_deepl(text, source_lang, target_lang)
        elif self.translation_service.get() == 'bhashaindia':
            return self.translate_with_bhashaindia(text, source_lang, target_lang)
        else:
            return self.translate_with_google(text, source_lang, target_lang)

    def process_translation(self):
        """Process the translation phase"""
        if not self.current_project_path:
            messagebox.showerror("Error", "Please complete masking phase first or load a project")
            return

        try:
            self.trans_progress.configure(mode='determinate')
            self.trans_results.delete(1.0, tk.END)
            self.log_message("🌐 Starting Phase 2: Safe Language Translation", self.trans_results)

            # Load masked dataset
            masked_file = os.path.join(self.current_project_path, f"{self.project_name.get()}_masked.{self.output_format.get()}")

            if not os.path.exists(masked_file):
                raise FileNotFoundError("Masked dataset not found. Please complete masking phase first.")

            self.log_message(f"📂 Loading masked dataset: {masked_file}", self.trans_results)

            if masked_file.endswith('.xlsx'):
                df = pd.read_excel(masked_file)
            else:
                df = pd.read_csv(masked_file)

            self.log_message(f"✅ Loaded {len(df)} rows", self.trans_results)

            # Get language codes
            source_lang = self.languages.get(self.source_lang.get(), self.source_lang.get())
            target_lang = self.languages.get(self.target_lang.get(), self.target_lang.get())

            self.log_message(f"🔤 Translating from {self.source_lang.get()} to {self.target_lang.get()}", self.trans_results)

            # Process translation
            df_translated = df.copy()
            columns_to_translate = [col for col in df.columns if col.startswith('Masked_')]

            total_cells = len(df) * len(columns_to_translate)
            processed_cells = 0

            for column in columns_to_translate:
                self.log_message(f"🔄 Translating {column}...", self.trans_results)

                translated_data = []
                batch = []
                batch_indices = []

                for i, text in enumerate(df[column]):
                    if isinstance(text, str) and text.strip():
                        batch.append(text)
                        batch_indices.append(i)

                        # Process batch when full or at end
                        if len(batch) >= self.batch_size.get() or i == len(df) - 1:
                            for j, batch_text in enumerate(batch):
                                try:
                                    translated = self.translate_text(batch_text, source_lang, target_lang)
                                    translated_data.append((batch_indices[j], translated))

                                    processed_cells += 1
                                    progress = (processed_cells / total_cells) * 100
                                    self.trans_progress['value'] = progress
                                    self.root.update()

                                    # Delay between requests
                                    time.sleep(self.request_delay.get())

                                except Exception as e:
                                    self.log_message(f"⚠️ Translation error for row {batch_indices[j]}: {str(e)}", self.trans_results)
                                    translated_data.append((batch_indices[j], batch_text))  # Keep original

                            batch = []
                            batch_indices = []
                    else:
                        translated_data.append((i, text))  # Keep original for non-text
                        processed_cells += 1

                # Apply translations
                translated_column = [None] * len(df)
                for idx, translated_text in translated_data:
                    translated_column[idx] = translated_text

                df_translated[f'Translated_{column.replace("Masked_", "")}'] = translated_column
                self.log_message(f"✅ {column} translation completed", self.trans_results)

            # Save translated dataset
            translated_file = os.path.join(self.current_project_path, f"{self.project_name.get()}_translated.{self.output_format.get()}")
            if self.output_format.get() == 'xlsx':
                df_translated.to_excel(translated_file, index=False)
            else:
                df_translated.to_csv(translated_file, index=False)

            self.log_message(f"💾 Translated dataset saved: {translated_file}", self.trans_results)

            # Summary
            self.log_message(f"\n📊 TRANSLATION SUMMARY:", self.trans_results)
            self.log_message(f"   • Total rows translated: {len(df)}", self.trans_results)
            self.log_message(f"   • Columns translated: {len(columns_to_translate)}", self.trans_results)
            self.log_message(f"   • Translation service: {self.translation_service.get().title()}", self.trans_results)
            self.log_message(f"   • Language pair: {self.source_lang.get()} → {self.target_lang.get()}", self.trans_results)

            messagebox.showinfo("Success", f"Translation completed!\n\nTranslated {len(df)} rows\nFile saved to: {translated_file}")

        except Exception as e:
            self.log_message(f"❌ ERROR: {str(e)}", self.trans_results)
            messagebox.showerror("Error", f"Translation failed: {str(e)}")
        finally:
            self.trans_progress['value'] = 0

    # ============================================================================
    # PHASE 3: RESTORATION FUNCTIONALITY
    # ============================================================================

    def restore_mathematical_expressions(self, text, mapping):
        """Restore tokens back to original mathematical expressions"""
        if not isinstance(text, str) or pd.isna(text):
            return text

        restored_text = text

        # Sort tokens by length (longest first) to avoid partial replacements
        sorted_tokens = sorted(mapping.keys(), key=len, reverse=True)

        for token in sorted_tokens:
            if token in restored_text:
                original_expression = mapping[token]['original']
                restored_text = restored_text.replace(token, original_expression)

        return restored_text

    def validate_restoration(self, original_mapping, restored_text):
        """Validate that all tokens were properly restored"""
        remaining_tokens = []

        for token in original_mapping.keys():
            if token in restored_text:
                remaining_tokens.append(token)

        return remaining_tokens

    def process_restoration(self):
        """Process the restoration phase"""
        if not self.current_project_path:
            messagebox.showerror("Error", "Please complete previous phases first or load a project")
            return

        try:
            self.restore_progress.start()
            self.restore_results.delete(1.0, tk.END)
            self.log_message("🔁 Starting Phase 3: Mathematical Expression Restoration", self.restore_results)

            # Load translated dataset
            translated_file = os.path.join(self.current_project_path, f"{self.project_name.get()}_translated.{self.output_format.get()}")

            if not os.path.exists(translated_file):
                raise FileNotFoundError("Translated dataset not found. Please complete translation phase first.")

            self.log_message(f"📂 Loading translated dataset: {translated_file}", self.restore_results)

            if translated_file.endswith('.xlsx'):
                df = pd.read_excel(translated_file)
            else:
                df = pd.read_csv(translated_file)

            self.log_message(f"✅ Loaded {len(df)} rows", self.restore_results)

            # Load mapping table
            mapping_file = os.path.join(self.current_project_path, f"{self.project_name.get()}_mapping.csv")

            if not os.path.exists(mapping_file):
                raise FileNotFoundError("Mapping table not found. Please complete masking phase first.")

            self.log_message(f"🗺️ Loading mapping table: {mapping_file}", self.restore_results)

            df_mapping = pd.read_csv(mapping_file)

            # Create mapping dictionary
            mapping = {}
            for _, row in df_mapping.iterrows():
                mapping[row['Token']] = {
                    'original': row['Original_Expression'],
                    'description': row['Description']
                }

            self.log_message(f"✅ Loaded {len(mapping)} token mappings", self.restore_results)

            # Process restoration
            df_restored = df.copy()
            columns_to_restore = [col for col in df.columns if col.startswith('Translated_')]

            validation_results = {}

            for column in columns_to_restore:
                self.log_message(f"🔄 Restoring {column}...", self.restore_results)

                restored_data = []
                remaining_tokens_count = 0

                for i, text in enumerate(df[column]):
                    restored_text = self.restore_mathematical_expressions(text, mapping)
                    restored_data.append(restored_text)

                    # Validate restoration if enabled
                    if self.validate_restoration.get():
                        remaining_tokens = self.validate_restoration(mapping, restored_text)
                        if remaining_tokens:
                            remaining_tokens_count += len(remaining_tokens)

                    if i % 50 == 0:
                        self.root.update()

                df_restored[f'Final_{column.replace("Translated_", "")}'] = restored_data
                validation_results[column] = remaining_tokens_count

                self.log_message(f"✅ {column} restoration completed", self.restore_results)
                if self.validate_restoration.get() and remaining_tokens_count > 0:
                    self.log_message(f"⚠️ {remaining_tokens_count} tokens not restored in {column}", self.restore_results)

            # Save final dataset
            final_file = os.path.join(self.current_project_path, f"{self.project_name.get()}_FINAL.{self.output_format.get()}")
            if self.output_format.get() == 'xlsx':
                df_restored.to_excel(final_file, index=False)
            else:
                df_restored.to_csv(final_file, index=False)

            self.log_message(f"💾 Final dataset saved: {final_file}", self.restore_results)

            # Create comparison file if enabled
            if self.create_comparison.get():
                comparison_file = os.path.join(self.current_project_path, f"{self.project_name.get()}_comparison.html")
                self.create_comparison_report(df_restored, comparison_file)
                self.log_message(f"📊 Comparison report created: {comparison_file}", self.restore_results)

            # Summary
            total_remaining = sum(validation_results.values())
            success_rate = ((len(mapping) - total_remaining) / len(mapping)) * 100 if len(mapping) > 0 else 100

            self.log_message(f"\n📊 RESTORATION SUMMARY:", self.restore_results)
            self.log_message(f"   • Total rows processed: {len(df)}", self.restore_results)
            self.log_message(f"   • Columns restored: {len(columns_to_restore)}", self.restore_results)
            self.log_message(f"   • Original tokens: {len(mapping)}", self.restore_results)
            self.log_message(f"   • Tokens not restored: {total_remaining}", self.restore_results)
            self.log_message(f"   • Restoration success rate: {success_rate:.1f}%", self.restore_results)

            messagebox.showinfo("Success", f"Restoration completed!\n\nSuccess rate: {success_rate:.1f}%\nFinal file: {final_file}")

        except Exception as e:
            self.log_message(f"❌ ERROR: {str(e)}", self.restore_results)
            messagebox.showerror("Error", f"Restoration failed: {str(e)}")
        finally:
            self.restore_progress.stop()

    # ============================================================================
    # UTILITY METHODS
    # ============================================================================

    def create_project_folder(self):
        """Create project folder structure"""
        project_folder = os.path.join(self.output_folder.get(), self.project_name.get())
        os.makedirs(project_folder, exist_ok=True)

        # Save project info
        project_info = {
            'name': self.project_name.get(),
            'created': datetime.now().isoformat(),
            'input_file': self.input_file.get(),
            'source_lang': self.source_lang.get(),
            'target_lang': self.target_lang.get(),
            'translation_service': self.translation_service.get()
        }

        with open(os.path.join(project_folder, 'project_info.json'), 'w') as f:
            json.dump(project_info, f, indent=2)

        self.current_project_path = project_folder
        return project_folder

    def log_message(self, message, text_widget):
        """Log message to text widget"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        text_widget.insert(tk.END, f"[{timestamp}] {message}\n")
        text_widget.see(tk.END)
        self.root.update()

    def create_comparison_report(self, df, output_file):
        """Create HTML comparison report"""
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Translation Pipeline Comparison Report</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
                .comparison { margin: 20px 0; }
                .original { background-color: #ffe6e6; padding: 10px; margin: 5px 0; border-radius: 3px; }
                .translated { background-color: #e6f3ff; padding: 10px; margin: 5px 0; border-radius: 3px; }
                .final { background-color: #e6ffe6; padding: 10px; margin: 5px 0; border-radius: 3px; }
                .row-header { font-weight: bold; color: #333; margin: 15px 0 5px 0; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Mathematical Expression Translation Pipeline</h1>
                <h2>Comparison Report</h2>
                <p>Project: {project_name}</p>
                <p>Generated: {timestamp}</p>
            </div>
        """.format(
            project_name=self.project_name.get(),
            timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        )

        # Add sample comparisons (first 5 rows)
        for i in range(min(5, len(df))):
            html_content += f'<div class="comparison"><div class="row-header">Row {i+1}:</div>'

            # Show original, translated, and final for each column
            for col in df.columns:
                if col.startswith('Question') and not col.startswith('Masked_') and not col.startswith('Translated_'):
                    original_col = col
                    masked_col = f'Masked_{col}'
                    translated_col = f'Translated_{col}'
                    final_col = f'Final_{col}'

                    if all(c in df.columns for c in [original_col, translated_col, final_col]):
                        html_content += f'<h4>{col}:</h4>'
                        html_content += f'<div class="original"><strong>Original:</strong> {df.iloc[i][original_col]}</div>'
                        html_content += f'<div class="translated"><strong>Translated:</strong> {df.iloc[i][translated_col]}</div>'
                        html_content += f'<div class="final"><strong>Final:</strong> {df.iloc[i][final_col]}</div>'

            html_content += '</div>'

        html_content += """
        </body>
        </html>
        """

        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)

    def start_pipeline(self):
        """Start the complete pipeline"""
        if not self.input_file.get():
            messagebox.showerror("Error", "Please select an input file")
            return

        if not self.project_name.get():
            messagebox.showerror("Error", "Please enter a project name")
            return

        response = messagebox.askyesno("Confirm",
            "This will run the complete pipeline:\n\n"
            "1. 🎭 Mask mathematical expressions\n"
            "2. 🌐 Translate text content\n"
            "3. 🔁 Restore mathematical expressions\n\n"
            "This may take several minutes. Continue?")

        if response:
            # Switch to masking tab and start
            self.notebook.select(1)  # Masking tab
            self.root.after(100, self.run_complete_pipeline)

    def run_complete_pipeline(self):
        """Run the complete pipeline automatically"""
        try:
            # Phase 1: Masking
            self.process_masking()

            # Wait a moment
            self.root.after(2000, self.continue_pipeline_translation)

        except Exception as e:
            messagebox.showerror("Pipeline Error", f"Pipeline failed at masking phase: {str(e)}")

    def continue_pipeline_translation(self):
        """Continue pipeline with translation"""
        try:
            # Phase 2: Translation
            self.notebook.select(2)  # Translation tab
            self.process_translation()

            # Wait a moment
            self.root.after(2000, self.continue_pipeline_restoration)

        except Exception as e:
            messagebox.showerror("Pipeline Error", f"Pipeline failed at translation phase: {str(e)}")

    def continue_pipeline_restoration(self):
        """Continue pipeline with restoration"""
        try:
            # Phase 3: Restoration
            self.notebook.select(3)  # Restoration tab
            self.process_restoration()

            messagebox.showinfo("Pipeline Complete",
                "🎉 Complete pipeline finished successfully!\n\n"
                "All phases completed:\n"
                "✅ Masking\n"
                "✅ Translation\n"
                "✅ Restoration\n\n"
                f"Check the output folder: {self.current_project_path}")

        except Exception as e:
            messagebox.showerror("Pipeline Error", f"Pipeline failed at restoration phase: {str(e)}")

def main():
    root = tk.Tk()

    # Configure style
    style = ttk.Style()
    style.theme_use('clam')

    # Create and run the application
    app = MathTranslationPipeline(root)
    root.mainloop()

if __name__ == "__main__":
    main()
