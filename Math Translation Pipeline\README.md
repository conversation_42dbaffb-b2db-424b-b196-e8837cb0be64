# Mathematical Expression Translation Pipeline

A comprehensive tool for translating educational content while preserving mathematical expressions.

## 🎯 Purpose

This application solves the critical problem of translating educational content that contains mathematical expressions. Traditional translation tools corrupt LaTeX code and mathematical symbols, making the translated content unusable. Our pipeline ensures perfect preservation of mathematical content during translation.

## 🔄 Workflow: Mask → Translate → Restore

### 🎭 Phase 1: Masking
**Goal**: Replace all LaTeX and mathematical symbols with unique tokens to prevent corruption during translation.

**Process**:
- Input: Excel or CSV file with columns like Question, Answer Key, Solution
- Detect and mask:
  - LaTeX blocks (`\frac{a}{b}`, `\sqrt{x}`, etc.)
  - Unicode math symbols (∫, √, ², etc.)
  - Subscripts/superscripts
- Output:
  - Masked dataset (plain text + tokens)
  - Mapping table (token → original expression)

**Example**:
```
Original: What is the value of \frac{a}{b} when a = 4?
Masked:   What is the value of [MATH_001] when a = 4?
Mapping:  [MATH_001] → \frac{a}{b}
```

### 🌐 Phase 2: Translation
**Goal**: Translate plain English text while keeping tokens untouched.

**Process**:
- Use translation services (DeepL or Google Translate)
- Ensure tokens like `[MATH_001]` are excluded from translation
- Batch processing for efficiency
- Rate limiting to respect API limits

**Example**:
```
Masked English: What is the value of [MATH_001] when a = 4?
Translated Urdu: [MATH_001] کی قیمت کیا ہے جب a = 4؟
```

### 🔁 Phase 3: Restoration
**Goal**: Replace tokens with original LaTeX expressions using the mapping table.

**Process**:
- Load translated dataset + mapping table
- Replace each token with its corresponding LaTeX code
- Validate restoration completeness
- Generate comparison reports

**Example**:
```
Final Urdu: \frac{a}{b} کی قیمت کیا ہے جب a = 4؟
```

## 🚀 Quick Start

### Installation

1. **Download the application**:
   ```bash
   # Download all files to a folder named "Math Translation Pipeline"
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Launch the application**:
   ```bash
   python launch_pipeline.py
   ```

### Basic Usage

1. **Project Setup**:
   - Enter project name
   - Select input Excel/CSV file
   - Choose source and target languages
   - Configure translation service (DeepL recommended)

2. **Run Pipeline**:
   - Click "🚀 Start Pipeline" for automatic processing
   - Or run each phase manually using the tabs

3. **Get Results**:
   - Final translated file with restored mathematical expressions
   - Comparison report showing before/after
   - Complete project folder with all intermediate files

## 📋 Features

### ✅ Comprehensive Mathematical Support
- **LaTeX expressions**: `\frac{a}{b}`, `\sqrt{x}`, `\int`, `\sum`, etc.
- **Unicode symbols**: ∫, √, ∞, ±, ℝ, θ, etc.
- **Superscripts/subscripts**: x², y₁, a³, etc.
- **Complex expressions**: Complete integrals, nested fractions

### ✅ Translation Services
- **DeepL API**: Professional-grade translation (recommended)
- **Google Translate**: Free option (no API key required)
- **Batch processing**: Efficient handling of large datasets
- **Rate limiting**: Respects API limits

### ✅ User-Friendly Interface
- **Tabbed interface**: Clear workflow visualization
- **Progress tracking**: Real-time progress bars
- **Detailed logging**: Complete operation history
- **Error handling**: Graceful error recovery

### ✅ Project Management
- **Project folders**: Organized file structure
- **Settings persistence**: Save/load configurations
- **Backup creation**: Automatic file backups
- **Comparison reports**: HTML reports showing results

## 📁 File Structure

After processing, your project folder will contain:

```
project_name/
├── project_info.json           # Project metadata
├── project_name_masked.csv     # Phase 1: Masked dataset
├── project_name_mapping.csv    # Token mappings
├── project_name_translated.csv # Phase 2: Translated dataset
├── project_name_FINAL.csv      # Phase 3: Final result
└── project_name_comparison.html # Comparison report
```

## ⚙️ Configuration

### Translation Services

#### DeepL (Recommended)
- **Pros**: High-quality translation, supports many languages
- **Cons**: Requires API key (free tier available)
- **Setup**: Get free API key from https://www.deepl.com/pro-api

#### Google Translate
- **Pros**: Free, no API key required
- **Cons**: Lower quality, rate limits
- **Setup**: Install `googletrans` library

### Supported Languages
- English ↔ Urdu
- English ↔ Arabic
- English ↔ Hindi
- English ↔ Spanish
- English ↔ French
- English ↔ German
- And many more...

## 🔧 Advanced Usage

### Custom Token Format
Default: `[MATH_{:03d}]` → `[MATH_001]`, `[MATH_002]`, etc.

You can customize the token format in the masking configuration.

### Batch Processing
- Adjust batch size for translation efficiency
- Configure delays between API requests
- Monitor progress with real-time updates

### Validation
- Automatic restoration validation
- Comparison report generation
- Error detection and reporting

## 🐛 Troubleshooting

### Common Issues

1. **"API Key Invalid"**
   - Verify your DeepL API key
   - Check API key permissions
   - Try the test connection feature

2. **"Translation Failed"**
   - Check internet connection
   - Verify language codes
   - Try reducing batch size

3. **"Tokens Not Restored"**
   - Check mapping file integrity
   - Verify token format consistency
   - Review validation report

4. **"File Not Found"**
   - Ensure all phases completed successfully
   - Check project folder structure
   - Verify file permissions

### Performance Tips

- **Large files**: Process in smaller batches
- **API limits**: Increase delay between requests
- **Memory usage**: Close other applications for large datasets

## 📊 Example Results

### Input (English)
```
Question: Find the derivative of f(x) = \frac{x^2 + 1}{x - 1}
Solution: Using the quotient rule: f'(x) = \frac{(x-1)(2x) - (x^2+1)(1)}{(x-1)^2}
```

### Output (Urdu)
```
Question: f(x) = \frac{x^2 + 1}{x - 1} کا مشتق تلاش کریں
Solution: quotient rule استعمال کرتے ہوئے: f'(x) = \frac{(x-1)(2x) - (x^2+1)(1)}{(x-1)^2}
```

## 🤝 Support

For issues, questions, or feature requests:
1. Check the troubleshooting section
2. Review the error logs in the application
3. Verify your input file format
4. Test with a smaller sample file

## 📄 License

This tool is provided for educational and research purposes.

## 🙏 Acknowledgments

- DeepL for high-quality translation API
- Google Translate for free translation service
- Open source community for supporting libraries
