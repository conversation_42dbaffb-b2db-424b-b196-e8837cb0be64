import pandas as pd
import numpy as np

# Load the original Excel file
df = pd.read_excel("GPT TEST.xlsx")

print("=== CHECKING SOLUTION COLUMN IN ORIGINAL DATA ===")
print(f"Total rows: {len(df)}")
print()

# Check rows 12-16 specifically
print("Rows 12-16 Solution column:")
for i in range(11, min(16, len(df))):
    row_num = i + 1
    solution_value = df.iloc[i]['Solution']
    
    print(f"Row {row_num}:")
    print(f"  Solution type: {type(solution_value)}")
    print(f"  Is NaN: {pd.isna(solution_value)}")
    if not pd.isna(solution_value):
        print(f"  Solution content: {repr(solution_value)}")
    else:
        print(f"  Solution content: EMPTY/NaN")
    print()

# Check overall statistics
print("=== SOLUTION COLUMN STATISTICS ===")
total_rows = len(df)
empty_solutions = df['Solution'].isna().sum()
non_empty_solutions = total_rows - empty_solutions

print(f"Total rows: {total_rows}")
print(f"Empty solutions: {empty_solutions}")
print(f"Non-empty solutions: {non_empty_solutions}")
print(f"Percentage empty: {(empty_solutions/total_rows)*100:.1f}%")

# Show some examples of non-empty solutions
print("\n=== EXAMPLES OF NON-EMPTY SOLUTIONS ===")
non_empty_mask = df['Solution'].notna()
if non_empty_mask.any():
    non_empty_df = df[non_empty_mask].head(3)
    for idx, row in non_empty_df.iterrows():
        print(f"Row {idx+1}: {repr(row['Solution'])[:100]}...")
else:
    print("No non-empty solutions found!")
