import pandas as pd

# Load both sheets from the Excel file
print("=== CHECKING MASKED DATASET SHEET ===")
df_masked = pd.read_excel("masked_GPT_TEST_fresh.xlsx", sheet_name="Masked Dataset")

print("Columns in Masked Dataset:")
print(df_masked.columns.tolist())
print()

print("Rows 12-16 from Masked Dataset:")
for i in range(11, min(16, len(df_masked))):
    row_num = i + 1
    print(f"Row {row_num}:")
    print(f"  Masked Question: {df_masked.iloc[i]['Masked Question']}")
    print(f"  Masked Answer Key: {df_masked.iloc[i]['Masked Answer Key']}")
    print(f"  Masked Solution: {df_masked.iloc[i]['Masked Solution']}")
    print()

print("\n" + "="*80)
print("=== CHECKING MAPPING TABLE SHEET ===")
df_mapping = pd.read_excel("masked_GPT_TEST_fresh.xlsx", sheet_name="Mapping Table")

print("First 10 entries in Mapping Table:")
print(df_mapping.head(10))
print()
print(f"Total mappings: {len(df_mapping)}")
